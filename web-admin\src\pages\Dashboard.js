import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
} from '@mui/material';
import {
  People as PeopleIcon,
  AccountBalance as AccountBalanceIcon,
  Article as ArticleIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

// Mock data
const statsData = [
  {
    title: 'Ju<PERSON><PERSON> ya Watumiaji',
    value: '12,543',
    change: '+12%',
    icon: <PeopleIcon />,
    color: '#007AFF',
  },
  {
    title: 'Miamala ya Leo',
    value: 'TSH 2.4M',
    change: '+8%',
    icon: <AccountBalanceIcon />,
    color: '#4CAF50',
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON>',
    value: '1,234',
    change: '+15%',
    icon: <ArticleIcon />,
    color: '#FF9800',
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    value: '3,456',
    change: '+5%',
    icon: <TrendingUpIcon />,
    color: '#9C27B0',
  },
];

const chartData = [
  { name: 'Jan', watumiaji: 4000, miamala: 2400 },
  { name: 'Feb', watumiaji: 3000, miamala: 1398 },
  { name: 'Mar', watumiaji: 2000, miamala: 9800 },
  { name: 'Apr', watumiaji: 2780, miamala: 3908 },
  { name: 'May', watumiaji: 1890, miamala: 4800 },
  { name: 'Jun', watumiaji: 2390, miamala: 3800 },
];

const pieData = [
  { name: 'Watumiaji wa Kawaida', value: 8543, color: '#007AFF' },
  { name: 'Wakala', value: 2341, color: '#4CAF50' },
  { name: 'Wafanyabiashara', value: 1659, color: '#FF9800' },
];

const recentActivities = [
  {
    id: 1,
    user: 'John Mwangi',
    action: 'Amejisajili kwenye mfumo',
    time: '5 dakika zilizopita',
    type: 'user',
  },
  {
    id: 2,
    user: 'Mary Kimani',
    action: 'Ametuma TSH 50,000',
    time: '10 dakika zilizopita',
    type: 'transaction',
  },
  {
    id: 3,
    user: 'Peter Mushi',
    action: 'Ameongeza chapisho jipya',
    time: '15 dakika zilizopita',
    type: 'post',
  },
  {
    id: 4,
    user: 'Grace Ally',
    action: 'Amefungua akaunti ya ProPay',
    time: '20 dakika zilizopita',
    type: 'wallet',
  },
];

export default function Dashboard() {
  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        Dashboard
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {statsData.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 2,
                      backgroundColor: `${stat.color}20`,
                      color: stat.color,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={stat.change}
                  size="small"
                  color="success"
                  variant="outlined"
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Charts */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Takwimu za Mwezi
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="watumiaji"
                  stroke="#007AFF"
                  strokeWidth={2}
                  name="Watumiaji"
                />
                <Line
                  type="monotone"
                  dataKey="miamala"
                  stroke="#4CAF50"
                  strokeWidth={2}
                  name="Miamala"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Miamala ya Wiki
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="miamala" fill="#007AFF" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* User Distribution */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Mgawanyo wa Watumiaji
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <Box sx={{ mt: 2 }}>
              {pieData.map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1,
                  }}
                >
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: item.color,
                      mr: 1,
                    }}
                  />
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>
                    {item.name}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {item.value.toLocaleString()}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>

          {/* Recent Activities */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Shughuli za Hivi Karibuni
            </Typography>
            <List>
              {recentActivities.map((activity) => (
                <ListItem key={activity.id} sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor:
                          activity.type === 'user'
                            ? '#007AFF'
                            : activity.type === 'transaction'
                            ? '#4CAF50'
                            : activity.type === 'post'
                            ? '#FF9800'
                            : '#9C27B0',
                        width: 32,
                        height: 32,
                      }}
                    >
                      {activity.user.charAt(0)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {activity.user}
                      </Typography>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {activity.action}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.time}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
