import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Chip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  People,
  Moderation,
  AccountBalance,
  Event,
  Work,
  Support,
  AdminPanelSettings,
  Settings,
  Analytics,
  Notifications,
  AccountCircle,
  Logout,
  Security,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
    permission: 'VIEW_ANALYTICS',
  },
  {
    text: 'Watumiaji',
    icon: <People />,
    path: '/users',
    permission: 'VIEW_USERS',
  },
  {
    text: 'Udhi<PERSON><PERSON> wa <PERSON>',
    icon: <Moderation />,
    path: '/content',
    permission: 'VIEW_POSTS',
  },
  {
    text: 'ProPay Finance',
    icon: <AccountBalance />,
    path: '/finance',
    permission: 'VIEW_TRANSACTIONS',
  },
  {
    text: 'Matukio',
    icon: <Event />,
    path: '/events',
    permission: 'VIEW_EVENTS',
  },
  {
    text: 'Kazi na Zabuni',
    icon: <Work />,
    path: '/jobs',
    permission: 'VIEW_JOBS',
  },
  {
    text: 'Msaada',
    icon: <Support />,
    path: '/support',
    permission: 'VIEW_TICKETS',
  },
  {
    text: 'Waongozi',
    icon: <AdminPanelSettings />,
    path: '/admins',
    permission: 'MANAGE_ADMINS',
  },
  {
    text: 'Takwimu',
    icon: <Analytics />,
    path: '/analytics',
    permission: 'VIEW_ANALYTICS',
  },
  {
    text: 'Mipangilio',
    icon: <Settings />,
    path: '/settings',
    permission: 'CONFIGURE_SYSTEM',
  },
];

export default function Layout() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const { user, logout, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleProfileMenuClose();
    logout();
    navigate('/login');
  };

  const getRoleColor = (role) => {
    const colors = {
      SUPER_ADMIN: 'error',
      MODERATOR: 'primary',
      FINANCE_OFFICER: 'success',
      SUPPORT_TEAM: 'info',
      RECRUITER: 'warning',
      EVENT_OFFICER: 'secondary',
      JOURNALIST_ADMIN: 'default',
    };
    return colors[role] || 'default';
  };

  const getRoleLabel = (role) => {
    const labels = {
      SUPER_ADMIN: 'Super Admin',
      MODERATOR: 'Moderator',
      FINANCE_OFFICER: 'Finance Officer',
      SUPPORT_TEAM: 'Support Team',
      RECRUITER: 'Recruiter',
      EVENT_OFFICER: 'Event Officer',
      JOURNALIST_ADMIN: 'Journalist',
    };
    return labels[role] || role;
  };

  const drawer = (
    <div>
      {/* Logo and Title */}
      <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h6" noWrap component="div">
          ProChat Admin
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          Mfumo wa Uongozi
        </Typography>
      </Box>

      <Divider />

      {/* User Info */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Avatar
          sx={{ width: 60, height: 60, mx: 'auto', mb: 1 }}
          src={user?.profileImageUrl}
        >
          {user?.firstName?.[0]}{user?.lastName?.[0]}
        </Avatar>
        <Typography variant="subtitle1" noWrap>
          {user?.firstName} {user?.lastName}
        </Typography>
        <Chip
          label={getRoleLabel(user?.adminRole)}
          size="small"
          color={getRoleColor(user?.adminRole)}
          sx={{ mt: 1 }}
        />
      </Box>

      <Divider />

      {/* Navigation Menu */}
      <List>
        {menuItems.map((item) => {
          if (!hasPermission(item.permission)) return null;
          
          const isActive = location.pathname === item.path;
          
          return (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={isActive}
                onClick={() => navigate(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'white',
                    },
                  },
                }}
              >
                <ListItemIcon>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => item.path === location.pathname)?.text || 'ProChat Admin'}
          </Typography>

          {/* Notifications */}
          <IconButton color="inherit" sx={{ mr: 1 }}>
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* Profile Menu */}
          <IconButton
            color="inherit"
            onClick={handleProfileMenuOpen}
          >
            <AccountCircle />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleProfileMenuClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem onClick={handleProfileMenuClose}>
              <ListItemIcon>
                <AccountCircle />
              </ListItemIcon>
              Profaili Yangu
            </MenuItem>
            <MenuItem onClick={handleProfileMenuClose}>
              <ListItemIcon>
                <Security />
              </ListItemIcon>
              Usalama
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <Logout />
              </ListItemIcon>
              Toka
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          bgcolor: 'background.default',
          minHeight: 'calc(100vh - 64px)',
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
}
