-- ProChat Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE prochat_db;

-- Create roles table first
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(20) NOT NULL UNIQUE
);

-- Insert default roles
INSERT IGNORE INTO roles (name) VALUES
('ROLE_USER'),
('ROLE_ADMIN'),
('ROLE_AGENT'),
('ROLE_MERCHANT');

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone_number VARCHAR(15) NOT NULL UNIQUE,
    password VARCHAR(120) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    profile_image_url VARCHAR(500),
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED') DEFAULT 'ACTIVE',
    is_verified BOOLEAN DEFAULT FALSE,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone_number),
    INDEX idx_status (status)
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Create wallets table
CREATE TABLE IF NOT EXISTS wallets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    wallet_number VARCHAR(20) NOT NULL UNIQUE,
    status ENUM('ACTIVE', 'SUSPENDED', 'FROZEN', 'CLOSED') DEFAULT 'ACTIVE',
    pin_hash VARCHAR(255),
    is_pin_set BOOLEAN DEFAULT FALSE,
    daily_limit DECIMAL(15,2) DEFAULT 1000000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 10000000.00,
    total_received DECIMAL(15,2) DEFAULT 0.00,
    total_sent DECIMAL(15,2) DEFAULT 0.00,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_wallet_number (wallet_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL UNIQUE,
    sender_wallet_id BIGINT,
    receiver_wallet_id BIGINT,
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) DEFAULT 0.00,
    type ENUM('SEND_MONEY', 'RECEIVE_MONEY', 'DEPOSIT', 'WITHDRAWAL', 'BILL_PAYMENT',
              'GOVERNMENT_PAYMENT', 'AGENT_COMMISSION', 'MERCHANT_PAYMENT',
              'GIFT_RECEIVED', 'GIFT_SENT', 'ADVERTISEMENT_PAYMENT',
              'TICKET_PURCHASE', 'REFUND') NOT NULL,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING',
    description VARCHAR(500),
    reference_number VARCHAR(100),
    external_reference VARCHAR(100),
    failure_reason VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (sender_wallet_id) REFERENCES wallets(id),
    FOREIGN KEY (receiver_wallet_id) REFERENCES wallets(id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_sender_wallet (sender_wallet_id),
    INDEX idx_receiver_wallet (receiver_wallet_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create chats table
CREATE TABLE IF NOT EXISTS chats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_name VARCHAR(100),
    type ENUM('PRIVATE', 'GROUP', 'CHANNEL') DEFAULT 'PRIVATE',
    created_by BIGINT NOT NULL,
    last_message_content VARCHAR(500),
    last_message_time DATETIME,
    last_message_sender BIGINT,
    is_archived BOOLEAN DEFAULT FALSE,
    is_muted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (last_message_sender) REFERENCES users(id),
    INDEX idx_type (type),
    INDEX idx_created_by (created_by),
    INDEX idx_last_message_time (last_message_time)
);

-- Create chat_participants junction table
CREATE TABLE IF NOT EXISTS chat_participants (
    chat_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    PRIMARY KEY (chat_id, user_id),
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_id BIGINT NOT NULL,
    sender_id BIGINT NOT NULL,
    content TEXT,
    type ENUM('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'LOCATION', 'CONTACT', 'GIFT') DEFAULT 'TEXT',
    reply_to_message_id BIGINT,
    status ENUM('SENT', 'DELIVERED', 'READ', 'FAILED') DEFAULT 'SENT',
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    delivered_at DATETIME,
    read_at DATETIME,
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (reply_to_message_id) REFERENCES messages(id),
    INDEX idx_chat_id (chat_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);

-- Create message_media table
CREATE TABLE IF NOT EXISTS message_media (
    message_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id)
);

-- Create posts table
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    content TEXT,
    type ENUM('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'MIXED') DEFAULT 'TEXT',
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    impressions_count BIGINT DEFAULT 0,
    is_pinned BOOLEAN DEFAULT FALSE,
    status ENUM('ACTIVE', 'HIDDEN', 'REPORTED', 'DELETED') DEFAULT 'ACTIVE',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create post_media table
CREATE TABLE IF NOT EXISTS post_media (
    post_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    INDEX idx_post_id (post_id)
);

-- Insert default roles
INSERT INTO roles (name) VALUES
('ROLE_USER'),
('ROLE_ADMIN'),
('ROLE_AGENT'),
('ROLE_MERCHANT');

-- Create agents table
CREATE TABLE agents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    agent_number VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(255),
    business_license VARCHAR(255),
    location VARCHAR(255),
    latitude DOUBLE,
    longitude DOUBLE,
    status ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED') DEFAULT 'PENDING',
    commission_rate DECIMAL(5,4) DEFAULT 0.01,
    daily_limit DECIMAL(15,2) DEFAULT 500000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 10000000.00,
    total_transactions DECIMAL(15,2) DEFAULT 0.00,
    total_commission DECIMAL(15,2) DEFAULT 0.00,
    current_float DECIMAL(15,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT,
    supervisor_agent_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_agent_number (agent_number),
    INDEX idx_agent_status (status),
    INDEX idx_agent_location (latitude, longitude)
);

-- Create merchants table
CREATE TABLE merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    merchant_number VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    business_type VARCHAR(100),
    business_license VARCHAR(255),
    tin_number VARCHAR(50),
    business_address TEXT,
    latitude DOUBLE,
    longitude DOUBLE,
    status ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED') DEFAULT 'PENDING',
    commission_rate DECIMAL(5,4) DEFAULT 0.02,
    daily_limit DECIMAL(15,2) DEFAULT 1000000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 20000000.00,
    total_sales DECIMAL(15,2) DEFAULT 0.00,
    total_commission_paid DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT,
    website_url VARCHAR(255),
    description TEXT,
    operating_hours VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_merchant_number (merchant_number),
    INDEX idx_merchant_status (status),
    INDEX idx_merchant_business_type (business_type)
);

-- Create advertisements table
CREATE TABLE advertisements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    ad_type ENUM('BANNER', 'VIDEO', 'CAROUSEL', 'STORY', 'SPONSORED_POST', 'NATIVE'),
    business_link VARCHAR(500),
    contact_info VARCHAR(255),
    target_age_min INT,
    target_age_max INT,
    target_gender ENUM('MALE', 'FEMALE', 'ALL') DEFAULT 'ALL',
    target_location VARCHAR(255),
    target_radius_km DOUBLE,
    target_interests TEXT,
    budget_amount DECIMAL(15,2),
    cost_per_view DECIMAL(10,4) DEFAULT 0.10,
    cost_per_click DECIMAL(10,4) DEFAULT 0.50,
    total_spent DECIMAL(15,2) DEFAULT 0.00,
    views_count BIGINT DEFAULT 0,
    clicks_count BIGINT DEFAULT 0,
    conversions_count BIGINT DEFAULT 0,
    impressions_count BIGINT DEFAULT 0,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    status ENUM('PENDING', 'APPROVED', 'LIVE', 'PAUSED', 'COMPLETED', 'REJECTED', 'EXPIRED') DEFAULT 'PENDING',
    rejection_reason TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    priority_level INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ad_status (status),
    INDEX idx_ad_dates (start_date, end_date),
    INDEX idx_ad_targeting (target_location, target_age_min, target_age_max)
);

-- Create advertisement_media table
CREATE TABLE advertisement_media (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    advertisement_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    media_type ENUM('IMAGE', 'VIDEO', 'AUDIO') DEFAULT 'IMAGE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (advertisement_id) REFERENCES advertisements(id) ON DELETE CASCADE
);

-- Create events table
CREATE TABLE events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    organizer_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('MUSIC', 'SPORTS', 'CONFERENCE', 'WORKSHOP', 'PARTY', 'EXHIBITION', 'THEATER', 'COMEDY', 'FESTIVAL', 'BUSINESS', 'EDUCATION', 'CHARITY', 'RELIGIOUS', 'OTHER'),
    venue_name VARCHAR(255),
    venue_address TEXT,
    latitude DOUBLE,
    longitude DOUBLE,
    event_date TIMESTAMP,
    end_date TIMESTAMP,
    ticket_price DECIMAL(15,2),
    total_tickets INT,
    available_tickets INT,
    sold_tickets INT DEFAULT 0,
    max_tickets_per_person INT DEFAULT 10,
    status ENUM('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'LIVE', 'SOLD_OUT', 'CANCELLED', 'COMPLETED', 'REJECTED') DEFAULT 'DRAFT',
    is_featured BOOLEAN DEFAULT FALSE,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    terms_and_conditions TEXT,
    refund_policy TEXT,
    age_restriction VARCHAR(50),
    dress_code VARCHAR(255),
    total_revenue DECIMAL(15,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.05,
    commission_amount DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_event_date (event_date),
    INDEX idx_event_status (status),
    INDEX idx_event_category (category)
);

-- Create event_images table
CREATE TABLE event_images (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Create tickets table
CREATE TABLE tickets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    buyer_id BIGINT NOT NULL,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    qr_code VARCHAR(255) UNIQUE NOT NULL,
    quantity INT DEFAULT 1,
    unit_price DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    service_fee DECIMAL(15,2) DEFAULT 0.00,
    final_amount DECIMAL(15,2),
    status ENUM('ACTIVE', 'USED', 'CANCELLED', 'REFUNDED', 'EXPIRED', 'TRANSFERRED') DEFAULT 'ACTIVE',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    buyer_name VARCHAR(255),
    buyer_email VARCHAR(255),
    buyer_phone VARCHAR(20),
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP NULL,
    used_by VARCHAR(255),
    check_in_location VARCHAR(255),
    special_requirements TEXT,
    seat_number VARCHAR(20),
    section VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ticket_number (ticket_number),
    INDEX idx_qr_code (qr_code),
    INDEX idx_ticket_status (status)
);

-- Create live_streams table
CREATE TABLE live_streams (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    streamer_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    stream_key VARCHAR(255) UNIQUE,
    stream_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    category ENUM('MUSIC', 'GAMING', 'TALK_SHOW', 'EDUCATION', 'COOKING', 'FITNESS', 'BUSINESS', 'NEWS', 'ENTERTAINMENT', 'SPORTS', 'TECHNOLOGY', 'LIFESTYLE', 'TRAVEL', 'ART', 'COMEDY', 'RELIGIOUS', 'OTHER'),
    status ENUM('SCHEDULED', 'LIVE', 'ENDED', 'CANCELLED', 'PAUSED', 'TECHNICAL_ISSUES') DEFAULT 'SCHEDULED',
    is_private BOOLEAN DEFAULT FALSE,
    password VARCHAR(255),
    max_viewers INT DEFAULT 1000,
    current_viewers INT DEFAULT 0,
    total_viewers INT DEFAULT 0,
    peak_viewers INT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    total_gifts_value DECIMAL(15,2) DEFAULT 0.00,
    scheduled_start TIMESTAMP,
    actual_start TIMESTAMP,
    ended_at TIMESTAMP,
    duration_minutes INT DEFAULT 0,
    is_recorded BOOLEAN DEFAULT TRUE,
    recording_url VARCHAR(500),
    quality_settings TEXT,
    chat_enabled BOOLEAN DEFAULT TRUE,
    gifts_enabled BOOLEAN DEFAULT TRUE,
    moderation_enabled BOOLEAN DEFAULT TRUE,
    tags TEXT,
    language VARCHAR(10) DEFAULT 'sw',
    age_restriction VARCHAR(10) DEFAULT 'ALL',
    monetization_enabled BOOLEAN DEFAULT FALSE,
    subscription_required BOOLEAN DEFAULT FALSE,
    subscription_price DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (streamer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_stream_status (status),
    INDEX idx_stream_category (category),
    INDEX idx_stream_date (scheduled_start)
);

-- Create gifts table
CREATE TABLE gifts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT NOT NULL,
    receiver_id BIGINT NOT NULL,
    post_id BIGINT NULL,
    live_stream_id BIGINT NULL,
    gift_type ENUM('HEART', 'ROSE', 'DIAMOND', 'CROWN', 'STAR', 'FIRE', 'ROCKET', 'CAKE', 'COFFEE', 'BEER', 'PIZZA', 'BURGER', 'ICE_CREAM', 'CHOCOLATE', 'PERFUME', 'WATCH', 'RING', 'NECKLACE', 'CAR', 'HOUSE', 'PLANE', 'YACHT', 'CUSTOM') NOT NULL,
    gift_name VARCHAR(100) NOT NULL,
    gift_icon_url VARCHAR(500),
    gift_animation_url VARCHAR(500),
    value DECIMAL(10,2) NOT NULL,
    quantity INT DEFAULT 1,
    total_amount DECIMAL(15,2),
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    status ENUM('SENT', 'RECEIVED', 'PENDING', 'FAILED', 'REFUNDED') DEFAULT 'SENT',
    transaction_reference VARCHAR(100),
    platform_commission DECIMAL(10,4) DEFAULT 0.10,
    commission_amount DECIMAL(15,2),
    receiver_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE SET NULL,
    FOREIGN KEY (live_stream_id) REFERENCES live_streams(id) ON DELETE SET NULL,
    INDEX idx_gift_sender (sender_id),
    INDEX idx_gift_receiver (receiver_id),
    INDEX idx_gift_type (gift_type)
);

-- Create notifications table
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('NEW_FOLLOWER', 'POST_LIKED', 'POST_COMMENTED', 'POST_SHARED', 'MENTION', 'NEW_MESSAGE', 'MISSED_CALL', 'VIDEO_CALL', 'VOICE_CALL', 'MONEY_RECEIVED', 'MONEY_SENT', 'PAYMENT_SUCCESSFUL', 'PAYMENT_FAILED', 'LOW_BALANCE', 'TRANSACTION_LIMIT_REACHED', 'GIFT_RECEIVED', 'GIFT_SENT', 'STREAM_STARTED', 'STREAM_ENDED', 'STREAM_REMINDER', 'EVENT_REMINDER', 'TICKET_PURCHASED', 'EVENT_CANCELLED', 'EVENT_UPDATED', 'ACCOUNT_VERIFIED', 'SECURITY_ALERT', 'SYSTEM_MAINTENANCE', 'APP_UPDATE', 'WELCOME', 'AD_APPROVED', 'AD_REJECTED', 'AD_EXPIRED', 'AGENT_APPLICATION_STATUS', 'MERCHANT_APPLICATION_STATUS', 'COMMISSION_EARNED', 'ANNOUNCEMENT', 'PROMOTION', 'REMINDER', 'WARNING', 'ERROR') NOT NULL,
    icon_url VARCHAR(500),
    action_url VARCHAR(500),
    action_data TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    priority INT DEFAULT 1,
    expires_at TIMESTAMP NULL,
    related_entity_type VARCHAR(50),
    related_entity_id BIGINT,
    sender_id BIGINT,
    group_key VARCHAR(100),
    is_push_sent BOOLEAN DEFAULT FALSE,
    is_email_sent BOOLEAN DEFAULT FALSE,
    is_sms_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notification_user (user_id),
    INDEX idx_notification_type (type),
    INDEX idx_notification_read (is_read),
    INDEX idx_notification_priority (priority)
);

-- Create meetings table
CREATE TABLE meetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    host_id BIGINT NOT NULL,
    meeting_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    password VARCHAR(255),
    join_url VARCHAR(500) NOT NULL,
    host_url VARCHAR(500),
    scheduled_start TIMESTAMP,
    scheduled_end TIMESTAMP,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    duration_minutes INT,
    timezone VARCHAR(50) DEFAULT 'Africa/Dar_es_Salaam',
    status ENUM('SCHEDULED', 'WAITING', 'IN_PROGRESS', 'ENDED', 'CANCELLED', 'EXPIRED') DEFAULT 'SCHEDULED',
    type ENUM('INSTANT', 'SCHEDULED', 'RECURRING', 'PERSONAL_ROOM', 'WEBINAR', 'CONFERENCE') DEFAULT 'INSTANT',
    max_participants INT DEFAULT 100,
    current_participants INT DEFAULT 0,
    total_participants INT DEFAULT 0,
    is_recording_enabled BOOLEAN DEFAULT FALSE,
    is_recording_active BOOLEAN DEFAULT FALSE,
    recording_url VARCHAR(500),
    is_waiting_room_enabled BOOLEAN DEFAULT TRUE,
    is_mute_on_entry BOOLEAN DEFAULT TRUE,
    is_video_on_entry BOOLEAN DEFAULT FALSE,
    is_chat_enabled BOOLEAN DEFAULT TRUE,
    is_screen_share_enabled BOOLEAN DEFAULT TRUE,
    is_translation_enabled BOOLEAN DEFAULT FALSE,
    primary_language VARCHAR(10) DEFAULT 'sw',
    agenda TEXT,
    meeting_notes TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern TEXT,
    parent_meeting_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_meeting_id (meeting_id),
    INDEX idx_meeting_status (status),
    INDEX idx_meeting_date (scheduled_start)
);

-- Create meeting_languages table
CREATE TABLE meeting_languages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    meeting_id BIGINT NOT NULL,
    language VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE
);
