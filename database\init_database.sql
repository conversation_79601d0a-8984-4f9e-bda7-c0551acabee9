-- =============================================
-- PROCHAT DATABASE SCHEMA
-- Complete Social & Financial Platform
-- WITH ADVANCED MONETIZATION FEATURES
-- =============================================

-- ProChat Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE prochat_db;

-- Create roles table first
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(20) NOT NULL UNIQUE
);

-- Insert default roles
INSERT IGNORE INTO roles (name) VALUES
('ROLE_USER'),
('ROLE_ADMIN'),
('ROLE_AGENT'),
('ROLE_MERCHANT');

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone_number VARCHAR(15) NOT NULL UNIQUE,
    password VARCHAR(120) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    profile_image_url VARCHAR(500),
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED') DEFAULT 'ACTIVE',
    is_verified BOOLEAN DEFAULT FALSE,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone_number),
    INDEX idx_status (status)
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Create wallets table
CREATE TABLE IF NOT EXISTS wallets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    wallet_number VARCHAR(20) NOT NULL UNIQUE,
    status ENUM('ACTIVE', 'SUSPENDED', 'FROZEN', 'CLOSED') DEFAULT 'ACTIVE',
    pin_hash VARCHAR(255),
    is_pin_set BOOLEAN DEFAULT FALSE,
    daily_limit DECIMAL(15,2) DEFAULT 1000000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 10000000.00,
    total_received DECIMAL(15,2) DEFAULT 0.00,
    total_sent DECIMAL(15,2) DEFAULT 0.00,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_wallet_number (wallet_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL UNIQUE,
    sender_wallet_id BIGINT,
    receiver_wallet_id BIGINT,
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) DEFAULT 0.00,
    type ENUM('SEND_MONEY', 'RECEIVE_MONEY', 'DEPOSIT', 'WITHDRAWAL', 'BILL_PAYMENT',
              'GOVERNMENT_PAYMENT', 'AGENT_COMMISSION', 'MERCHANT_PAYMENT',
              'GIFT_RECEIVED', 'GIFT_SENT', 'ADVERTISEMENT_PAYMENT',
              'TICKET_PURCHASE', 'REFUND') NOT NULL,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING',
    description VARCHAR(500),
    reference_number VARCHAR(100),
    external_reference VARCHAR(100),
    failure_reason VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (sender_wallet_id) REFERENCES wallets(id),
    FOREIGN KEY (receiver_wallet_id) REFERENCES wallets(id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_sender_wallet (sender_wallet_id),
    INDEX idx_receiver_wallet (receiver_wallet_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create chats table
CREATE TABLE IF NOT EXISTS chats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_name VARCHAR(100),
    type ENUM('PRIVATE', 'GROUP', 'CHANNEL') DEFAULT 'PRIVATE',
    created_by BIGINT NOT NULL,
    last_message_content VARCHAR(500),
    last_message_time DATETIME,
    last_message_sender BIGINT,
    is_archived BOOLEAN DEFAULT FALSE,
    is_muted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (last_message_sender) REFERENCES users(id),
    INDEX idx_type (type),
    INDEX idx_created_by (created_by),
    INDEX idx_last_message_time (last_message_time)
);

-- Create chat_participants junction table
CREATE TABLE IF NOT EXISTS chat_participants (
    chat_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    PRIMARY KEY (chat_id, user_id),
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_id BIGINT NOT NULL,
    sender_id BIGINT NOT NULL,
    content TEXT,
    type ENUM('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'LOCATION', 'CONTACT', 'GIFT') DEFAULT 'TEXT',
    reply_to_message_id BIGINT,
    status ENUM('SENT', 'DELIVERED', 'READ', 'FAILED') DEFAULT 'SENT',
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    delivered_at DATETIME,
    read_at DATETIME,
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (reply_to_message_id) REFERENCES messages(id),
    INDEX idx_chat_id (chat_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);

-- Create message_media table
CREATE TABLE IF NOT EXISTS message_media (
    message_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id)
);

-- Create posts table
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    content TEXT,
    type ENUM('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'MIXED') DEFAULT 'TEXT',
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    impressions_count BIGINT DEFAULT 0,
    is_pinned BOOLEAN DEFAULT FALSE,
    status ENUM('ACTIVE', 'HIDDEN', 'REPORTED', 'DELETED') DEFAULT 'ACTIVE',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create post_media table
CREATE TABLE IF NOT EXISTS post_media (
    post_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    INDEX idx_post_id (post_id)
);

-- Insert default roles
INSERT INTO roles (name) VALUES
('ROLE_USER'),
('ROLE_ADMIN'),
('ROLE_AGENT'),
('ROLE_MERCHANT');

-- Create agents table
CREATE TABLE agents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    agent_number VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(255),
    business_license VARCHAR(255),
    location VARCHAR(255),
    latitude DOUBLE,
    longitude DOUBLE,
    status ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED') DEFAULT 'PENDING',
    commission_rate DECIMAL(5,4) DEFAULT 0.01,
    daily_limit DECIMAL(15,2) DEFAULT 500000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 10000000.00,
    total_transactions DECIMAL(15,2) DEFAULT 0.00,
    total_commission DECIMAL(15,2) DEFAULT 0.00,
    current_float DECIMAL(15,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT,
    supervisor_agent_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_agent_number (agent_number),
    INDEX idx_agent_status (status),
    INDEX idx_agent_location (latitude, longitude)
);

-- Create merchants table
CREATE TABLE merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    merchant_number VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    business_type VARCHAR(100),
    business_license VARCHAR(255),
    tin_number VARCHAR(50),
    business_address TEXT,
    latitude DOUBLE,
    longitude DOUBLE,
    status ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED') DEFAULT 'PENDING',
    commission_rate DECIMAL(5,4) DEFAULT 0.02,
    daily_limit DECIMAL(15,2) DEFAULT 1000000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 20000000.00,
    total_sales DECIMAL(15,2) DEFAULT 0.00,
    total_commission_paid DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents TEXT,
    website_url VARCHAR(255),
    description TEXT,
    operating_hours VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_merchant_number (merchant_number),
    INDEX idx_merchant_status (status),
    INDEX idx_merchant_business_type (business_type)
);

-- Create advertisements table
CREATE TABLE advertisements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    ad_type ENUM('BANNER', 'VIDEO', 'CAROUSEL', 'STORY', 'SPONSORED_POST', 'NATIVE'),
    business_link VARCHAR(500),
    contact_info VARCHAR(255),
    target_age_min INT,
    target_age_max INT,
    target_gender ENUM('MALE', 'FEMALE', 'ALL') DEFAULT 'ALL',
    target_location VARCHAR(255),
    target_radius_km DOUBLE,
    target_interests TEXT,
    budget_amount DECIMAL(15,2),
    cost_per_view DECIMAL(10,4) DEFAULT 0.10,
    cost_per_click DECIMAL(10,4) DEFAULT 0.50,
    total_spent DECIMAL(15,2) DEFAULT 0.00,
    views_count BIGINT DEFAULT 0,
    clicks_count BIGINT DEFAULT 0,
    conversions_count BIGINT DEFAULT 0,
    impressions_count BIGINT DEFAULT 0,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    status ENUM('PENDING', 'APPROVED', 'LIVE', 'PAUSED', 'COMPLETED', 'REJECTED', 'EXPIRED') DEFAULT 'PENDING',
    rejection_reason TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    priority_level INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ad_status (status),
    INDEX idx_ad_dates (start_date, end_date),
    INDEX idx_ad_targeting (target_location, target_age_min, target_age_max)
);

-- Create advertisement_media table
CREATE TABLE advertisement_media (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    advertisement_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    media_type ENUM('IMAGE', 'VIDEO', 'AUDIO') DEFAULT 'IMAGE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (advertisement_id) REFERENCES advertisements(id) ON DELETE CASCADE
);

-- Create events table
CREATE TABLE events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    organizer_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('MUSIC', 'SPORTS', 'CONFERENCE', 'WORKSHOP', 'PARTY', 'EXHIBITION', 'THEATER', 'COMEDY', 'FESTIVAL', 'BUSINESS', 'EDUCATION', 'CHARITY', 'RELIGIOUS', 'OTHER'),
    venue_name VARCHAR(255),
    venue_address TEXT,
    latitude DOUBLE,
    longitude DOUBLE,
    event_date TIMESTAMP,
    end_date TIMESTAMP,
    ticket_price DECIMAL(15,2),
    total_tickets INT,
    available_tickets INT,
    sold_tickets INT DEFAULT 0,
    max_tickets_per_person INT DEFAULT 10,
    status ENUM('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'LIVE', 'SOLD_OUT', 'CANCELLED', 'COMPLETED', 'REJECTED') DEFAULT 'DRAFT',
    is_featured BOOLEAN DEFAULT FALSE,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    terms_and_conditions TEXT,
    refund_policy TEXT,
    age_restriction VARCHAR(50),
    dress_code VARCHAR(255),
    total_revenue DECIMAL(15,2) DEFAULT 0.00,
    commission_rate DECIMAL(5,4) DEFAULT 0.05,
    commission_amount DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_event_date (event_date),
    INDEX idx_event_status (status),
    INDEX idx_event_category (category)
);

-- Create event_images table
CREATE TABLE event_images (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Create tickets table
CREATE TABLE tickets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    buyer_id BIGINT NOT NULL,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    qr_code VARCHAR(255) UNIQUE NOT NULL,
    quantity INT DEFAULT 1,
    unit_price DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    service_fee DECIMAL(15,2) DEFAULT 0.00,
    final_amount DECIMAL(15,2),
    status ENUM('ACTIVE', 'USED', 'CANCELLED', 'REFUNDED', 'EXPIRED', 'TRANSFERRED') DEFAULT 'ACTIVE',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    buyer_name VARCHAR(255),
    buyer_email VARCHAR(255),
    buyer_phone VARCHAR(20),
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP NULL,
    used_by VARCHAR(255),
    check_in_location VARCHAR(255),
    special_requirements TEXT,
    seat_number VARCHAR(20),
    section VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ticket_number (ticket_number),
    INDEX idx_qr_code (qr_code),
    INDEX idx_ticket_status (status)
);

-- Create live_streams table
CREATE TABLE live_streams (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    streamer_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    stream_key VARCHAR(255) UNIQUE,
    stream_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    category ENUM('MUSIC', 'GAMING', 'TALK_SHOW', 'EDUCATION', 'COOKING', 'FITNESS', 'BUSINESS', 'NEWS', 'ENTERTAINMENT', 'SPORTS', 'TECHNOLOGY', 'LIFESTYLE', 'TRAVEL', 'ART', 'COMEDY', 'RELIGIOUS', 'OTHER'),
    status ENUM('SCHEDULED', 'LIVE', 'ENDED', 'CANCELLED', 'PAUSED', 'TECHNICAL_ISSUES') DEFAULT 'SCHEDULED',
    is_private BOOLEAN DEFAULT FALSE,
    password VARCHAR(255),
    max_viewers INT DEFAULT 1000,
    current_viewers INT DEFAULT 0,
    total_viewers INT DEFAULT 0,
    peak_viewers INT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    total_gifts_value DECIMAL(15,2) DEFAULT 0.00,
    scheduled_start TIMESTAMP,
    actual_start TIMESTAMP,
    ended_at TIMESTAMP,
    duration_minutes INT DEFAULT 0,
    is_recorded BOOLEAN DEFAULT TRUE,
    recording_url VARCHAR(500),
    quality_settings TEXT,
    chat_enabled BOOLEAN DEFAULT TRUE,
    gifts_enabled BOOLEAN DEFAULT TRUE,
    moderation_enabled BOOLEAN DEFAULT TRUE,
    tags TEXT,
    language VARCHAR(10) DEFAULT 'sw',
    age_restriction VARCHAR(10) DEFAULT 'ALL',
    monetization_enabled BOOLEAN DEFAULT FALSE,
    subscription_required BOOLEAN DEFAULT FALSE,
    subscription_price DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (streamer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_stream_status (status),
    INDEX idx_stream_category (category),
    INDEX idx_stream_date (scheduled_start)
);

-- Create gifts table
CREATE TABLE gifts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT NOT NULL,
    receiver_id BIGINT NOT NULL,
    post_id BIGINT NULL,
    live_stream_id BIGINT NULL,
    gift_type ENUM('HEART', 'ROSE', 'DIAMOND', 'CROWN', 'STAR', 'FIRE', 'ROCKET', 'CAKE', 'COFFEE', 'BEER', 'PIZZA', 'BURGER', 'ICE_CREAM', 'CHOCOLATE', 'PERFUME', 'WATCH', 'RING', 'NECKLACE', 'CAR', 'HOUSE', 'PLANE', 'YACHT', 'CUSTOM') NOT NULL,
    gift_name VARCHAR(100) NOT NULL,
    gift_icon_url VARCHAR(500),
    gift_animation_url VARCHAR(500),
    value DECIMAL(10,2) NOT NULL,
    quantity INT DEFAULT 1,
    total_amount DECIMAL(15,2),
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    status ENUM('SENT', 'RECEIVED', 'PENDING', 'FAILED', 'REFUNDED') DEFAULT 'SENT',
    transaction_reference VARCHAR(100),
    platform_commission DECIMAL(10,4) DEFAULT 0.10,
    commission_amount DECIMAL(15,2),
    receiver_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE SET NULL,
    FOREIGN KEY (live_stream_id) REFERENCES live_streams(id) ON DELETE SET NULL,
    INDEX idx_gift_sender (sender_id),
    INDEX idx_gift_receiver (receiver_id),
    INDEX idx_gift_type (gift_type)
);

-- Create notifications table
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('NEW_FOLLOWER', 'POST_LIKED', 'POST_COMMENTED', 'POST_SHARED', 'MENTION', 'NEW_MESSAGE', 'MISSED_CALL', 'VIDEO_CALL', 'VOICE_CALL', 'MONEY_RECEIVED', 'MONEY_SENT', 'PAYMENT_SUCCESSFUL', 'PAYMENT_FAILED', 'LOW_BALANCE', 'TRANSACTION_LIMIT_REACHED', 'GIFT_RECEIVED', 'GIFT_SENT', 'STREAM_STARTED', 'STREAM_ENDED', 'STREAM_REMINDER', 'EVENT_REMINDER', 'TICKET_PURCHASED', 'EVENT_CANCELLED', 'EVENT_UPDATED', 'ACCOUNT_VERIFIED', 'SECURITY_ALERT', 'SYSTEM_MAINTENANCE', 'APP_UPDATE', 'WELCOME', 'AD_APPROVED', 'AD_REJECTED', 'AD_EXPIRED', 'AGENT_APPLICATION_STATUS', 'MERCHANT_APPLICATION_STATUS', 'COMMISSION_EARNED', 'ANNOUNCEMENT', 'PROMOTION', 'REMINDER', 'WARNING', 'ERROR') NOT NULL,
    icon_url VARCHAR(500),
    action_url VARCHAR(500),
    action_data TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    priority INT DEFAULT 1,
    expires_at TIMESTAMP NULL,
    related_entity_type VARCHAR(50),
    related_entity_id BIGINT,
    sender_id BIGINT,
    group_key VARCHAR(100),
    is_push_sent BOOLEAN DEFAULT FALSE,
    is_email_sent BOOLEAN DEFAULT FALSE,
    is_sms_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notification_user (user_id),
    INDEX idx_notification_type (type),
    INDEX idx_notification_read (is_read),
    INDEX idx_notification_priority (priority)
);

-- Create meetings table
CREATE TABLE meetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    host_id BIGINT NOT NULL,
    meeting_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    password VARCHAR(255),
    join_url VARCHAR(500) NOT NULL,
    host_url VARCHAR(500),
    scheduled_start TIMESTAMP,
    scheduled_end TIMESTAMP,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    duration_minutes INT,
    timezone VARCHAR(50) DEFAULT 'Africa/Dar_es_Salaam',
    status ENUM('SCHEDULED', 'WAITING', 'IN_PROGRESS', 'ENDED', 'CANCELLED', 'EXPIRED') DEFAULT 'SCHEDULED',
    type ENUM('INSTANT', 'SCHEDULED', 'RECURRING', 'PERSONAL_ROOM', 'WEBINAR', 'CONFERENCE') DEFAULT 'INSTANT',
    max_participants INT DEFAULT 100,
    current_participants INT DEFAULT 0,
    total_participants INT DEFAULT 0,
    is_recording_enabled BOOLEAN DEFAULT FALSE,
    is_recording_active BOOLEAN DEFAULT FALSE,
    recording_url VARCHAR(500),
    is_waiting_room_enabled BOOLEAN DEFAULT TRUE,
    is_mute_on_entry BOOLEAN DEFAULT TRUE,
    is_video_on_entry BOOLEAN DEFAULT FALSE,
    is_chat_enabled BOOLEAN DEFAULT TRUE,
    is_screen_share_enabled BOOLEAN DEFAULT TRUE,
    is_translation_enabled BOOLEAN DEFAULT FALSE,
    primary_language VARCHAR(10) DEFAULT 'sw',
    agenda TEXT,
    meeting_notes TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern TEXT,
    parent_meeting_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_meeting_id (meeting_id),
    INDEX idx_meeting_status (status),
    INDEX idx_meeting_date (scheduled_start)
);

-- Create meeting_languages table
CREATE TABLE meeting_languages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    meeting_id BIGINT NOT NULL,
    language VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE
);

-- Create rewards table
CREATE TABLE rewards (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reward_type ENUM('CASH_BONUS', 'POINTS', 'DISCOUNT_COUPON', 'FREE_TRANSACTION', 'PREMIUM_FEATURE', 'GIFT_VOUCHER', 'AIRTIME', 'DATA_BUNDLE', 'SPECIAL_BADGE', 'EXCLUSIVE_ACCESS') NOT NULL,
    reward_value DECIMAL(15,2),
    points_required INT,
    icon_url VARCHAR(500),
    image_url VARCHAR(500),
    status ENUM('PENDING', 'AVAILABLE', 'CLAIMED', 'EXPIRED', 'CANCELLED') DEFAULT 'PENDING',
    earned_from VARCHAR(100),
    related_entity_id BIGINT,
    expires_at TIMESTAMP,
    claimed_at TIMESTAMP,
    is_featured BOOLEAN DEFAULT FALSE,
    terms_conditions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_reward_user (user_id),
    INDEX idx_reward_type (reward_type),
    INDEX idx_reward_status (status)
);

-- Create invitations table
CREATE TABLE invitations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    inviter_id BIGINT NOT NULL,
    invitee_id BIGINT,
    invitation_code VARCHAR(50) UNIQUE NOT NULL,
    invitation_link VARCHAR(500) NOT NULL,
    invitee_phone VARCHAR(20),
    invitee_email VARCHAR(255),
    invitee_name VARCHAR(255),
    status ENUM('SENT', 'VIEWED', 'ACCEPTED', 'EXPIRED', 'CANCELLED') DEFAULT 'SENT',
    bonus_amount DECIMAL(15,2) DEFAULT 5000.00,
    inviter_bonus DECIMAL(15,2) DEFAULT 2000.00,
    invitee_bonus DECIMAL(15,2) DEFAULT 3000.00,
    bonus_paid BOOLEAN DEFAULT FALSE,
    bonus_paid_at TIMESTAMP,
    joined_at TIMESTAMP,
    expires_at TIMESTAMP,
    message TEXT,
    invitation_method VARCHAR(50),
    campaign_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_invitation_code (invitation_code),
    INDEX idx_invitation_status (status)
);

-- Create games table
CREATE TABLE games (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    game_type ENUM('BALL_SHOOTER', 'COLOR_MATCHING', 'PUZZLE', 'ARCADE', 'STRATEGY', 'TRIVIA', 'WORD_GAME', 'MEMORY', 'RACING', 'ADVENTURE', 'CASUAL', 'MULTIPLAYER'),
    icon_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    game_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    min_players INT DEFAULT 1,
    max_players INT DEFAULT 1,
    difficulty_level INT DEFAULT 1,
    estimated_duration_minutes INT DEFAULT 5,
    reward_points INT DEFAULT 10,
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    total_plays BIGINT DEFAULT 0,
    total_players BIGINT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count BIGINT DEFAULT 0,
    instructions TEXT,
    tags TEXT,
    age_rating VARCHAR(10) DEFAULT 'ALL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_game_type (game_type),
    INDEX idx_game_active (is_active),
    INDEX idx_game_featured (is_featured)
);

-- Create diary_entries table
CREATE TABLE diary_entries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255),
    content TEXT,
    entry_date TIMESTAMP,
    mood ENUM('VERY_HAPPY', 'HAPPY', 'NEUTRAL', 'SAD', 'VERY_SAD', 'EXCITED', 'CALM', 'ANXIOUS', 'ANGRY', 'GRATEFUL', 'TIRED', 'ENERGETIC'),
    privacy_level ENUM('PRIVATE', 'FRIENDS_ONLY', 'PUBLIC') DEFAULT 'PRIVATE',
    location VARCHAR(255),
    weather VARCHAR(100),
    tags TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    reminder_set BOOLEAN DEFAULT FALSE,
    reminder_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_diary_user (user_id),
    INDEX idx_diary_date (entry_date),
    INDEX idx_diary_mood (mood)
);

-- Create diary_entry_media table
CREATE TABLE diary_entry_media (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    diary_entry_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (diary_entry_id) REFERENCES diary_entries(id) ON DELETE CASCADE
);

-- Create schedules table
CREATE TABLE schedules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    event_type ENUM('MEETING', 'APPOINTMENT', 'REMINDER', 'TASK', 'EVENT', 'BIRTHDAY', 'ANNIVERSARY', 'DEADLINE', 'CALL', 'TRAVEL', 'WORKOUT', 'MEAL', 'MEDICATION', 'OTHER'),
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM',
    location VARCHAR(255),
    is_all_day BOOLEAN DEFAULT FALSE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern TEXT,
    reminder_minutes_before INT DEFAULT 15,
    is_reminder_sent BOOLEAN DEFAULT FALSE,
    color_code VARCHAR(10) DEFAULT '#007AFF',
    notes TEXT,
    attendees TEXT,
    status ENUM('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'POSTPONED', 'MISSED') DEFAULT 'SCHEDULED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_schedule_user (user_id),
    INDEX idx_schedule_start (start_time),
    INDEX idx_schedule_status (status)
);

-- Create statuses table
CREATE TABLE statuses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    caption TEXT,
    media_type ENUM('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'GIF'),
    background_color VARCHAR(10) DEFAULT '#007AFF',
    text_color VARCHAR(10) DEFAULT '#FFFFFF',
    font_style VARCHAR(50) DEFAULT 'normal',
    privacy_setting ENUM('ALL_CONTACTS', 'CLOSE_FRIENDS', 'EXCEPT_CONTACTS', 'ONLY_SHARE_WITH', 'PUBLIC') DEFAULT 'ALL_CONTACTS',
    views_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    replies_count BIGINT DEFAULT 0,
    expires_at TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE,
    location VARCHAR(255),
    music_url VARCHAR(500),
    music_title VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status_user (user_id),
    INDEX idx_status_expires (expires_at)
);

-- Create status_media table
CREATE TABLE status_media (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    status_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (status_id) REFERENCES statuses(id) ON DELETE CASCADE
);

-- Create channels table
CREATE TABLE channels (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    owner_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    channel_handle VARCHAR(100) UNIQUE,
    avatar_url VARCHAR(500),
    cover_image_url VARCHAR(500),
    category ENUM('NEWS', 'ENTERTAINMENT', 'SPORTS', 'TECHNOLOGY', 'BUSINESS', 'EDUCATION', 'LIFESTYLE', 'HEALTH', 'TRAVEL', 'FOOD', 'MUSIC', 'COMEDY', 'POLITICS', 'SCIENCE', 'RELIGION', 'GOVERNMENT', 'LOCAL_NEWS', 'WEATHER', 'TRAFFIC', 'EMERGENCY', 'OTHER'),
    privacy_type ENUM('PUBLIC', 'PRIVATE', 'INVITE_ONLY', 'PAID_SUBSCRIPTION') DEFAULT 'PUBLIC',
    is_verified BOOLEAN DEFAULT FALSE,
    is_official BOOLEAN DEFAULT FALSE,
    subscribers_count BIGINT DEFAULT 0,
    posts_count BIGINT DEFAULT 0,
    total_views BIGINT DEFAULT 0,
    is_monetized BOOLEAN DEFAULT FALSE,
    subscription_fee DECIMAL(10,2) DEFAULT 0.00,
    invite_link VARCHAR(500),
    rules TEXT,
    contact_info VARCHAR(255),
    website_url VARCHAR(500),
    location VARCHAR(255),
    language VARCHAR(10) DEFAULT 'sw',
    tags TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_channel_handle (channel_handle),
    INDEX idx_channel_category (category),
    INDEX idx_channel_active (is_active)
);

-- Create news_articles table
CREATE TABLE news_articles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    author_id BIGINT,
    channel_id BIGINT,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(500),
    summary TEXT,
    content LONGTEXT,
    featured_image_url VARCHAR(500),
    category ENUM('BREAKING_NEWS', 'POLITICS', 'BUSINESS', 'SPORTS', 'ENTERTAINMENT', 'TECHNOLOGY', 'HEALTH', 'EDUCATION', 'WEATHER', 'TRAFFIC', 'CRIME', 'INTERNATIONAL', 'LOCAL', 'ECONOMY', 'AGRICULTURE', 'TOURISM', 'CULTURE', 'RELIGION', 'ENVIRONMENT', 'SCIENCE', 'LIFESTYLE', 'OPINION', 'EDITORIAL', 'OTHER'),
    tags TEXT,
    source VARCHAR(255),
    source_url VARCHAR(500),
    location VARCHAR(255),
    language VARCHAR(10) DEFAULT 'sw',
    status ENUM('DRAFT', 'PENDING_REVIEW', 'APPROVED', 'PUBLISHED', 'ARCHIVED', 'REJECTED', 'EXPIRED') DEFAULT 'DRAFT',
    is_featured BOOLEAN DEFAULT FALSE,
    is_breaking BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    premium_price DECIMAL(10,2) DEFAULT 0.00,
    views_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    bookmarks_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    total_gifts_value DECIMAL(15,2) DEFAULT 0.00,
    audio_url VARCHAR(500),
    audio_duration_seconds INT,
    pdf_url VARCHAR(500),
    reading_time_minutes INT,
    published_at TIMESTAMP,
    expires_at TIMESTAMP,
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE SET NULL,
    INDEX idx_news_category (category),
    INDEX idx_news_status (status),
    INDEX idx_news_published (published_at)
);

-- Create news_article_media table
CREATE TABLE news_article_media (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    news_article_id BIGINT NOT NULL,
    media_url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (news_article_id) REFERENCES news_articles(id) ON DELETE CASCADE
);

-- Create short_videos table
CREATE TABLE short_videos (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    creator_id BIGINT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    video_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    duration_seconds INT,
    file_size_bytes BIGINT,
    resolution VARCHAR(20),
    category ENUM('COMEDY', 'DANCE', 'MUSIC', 'EDUCATION', 'COOKING', 'FITNESS', 'BEAUTY', 'FASHION', 'TRAVEL', 'PETS', 'SPORTS', 'GAMING', 'TECHNOLOGY', 'DIY', 'ART', 'LIFESTYLE', 'NEWS', 'BUSINESS', 'MOTIVATION', 'ENTERTAINMENT', 'CULTURE', 'LANGUAGE', 'SCIENCE', 'NATURE', 'OTHER'),
    hashtags TEXT,
    music_url VARCHAR(500),
    music_title VARCHAR(255),
    music_artist VARCHAR(255),
    location VARCHAR(255),
    latitude DOUBLE,
    longitude DOUBLE,
    privacy_setting ENUM('PUBLIC', 'FRIENDS_ONLY', 'PRIVATE', 'UNLISTED') DEFAULT 'PUBLIC',
    allows_comments BOOLEAN DEFAULT TRUE,
    allows_duet BOOLEAN DEFAULT TRUE,
    allows_download BOOLEAN DEFAULT TRUE,
    is_monetized BOOLEAN DEFAULT FALSE,
    views_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    downloads_count BIGINT DEFAULT 0,
    gifts_count BIGINT DEFAULT 0,
    total_gifts_value DECIMAL(15,2) DEFAULT 0.00,
    total_watch_time_seconds BIGINT DEFAULT 0,
    average_watch_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    status ENUM('UPLOADING', 'PROCESSING', 'PUBLISHED', 'PRIVATE', 'DELETED', 'BLOCKED', 'FAILED') DEFAULT 'PROCESSING',
    moderation_status VARCHAR(20) DEFAULT 'PENDING',
    rejection_reason TEXT,
    age_restriction VARCHAR(10) DEFAULT 'ALL',
    language VARCHAR(10) DEFAULT 'sw',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_video_creator (creator_id),
    INDEX idx_video_category (category),
    INDEX idx_video_status (status),
    INDEX idx_video_trending (is_trending)
);

-- Create admin_users table
CREATE TABLE admin_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    profile_image_url VARCHAR(500),
    admin_role ENUM('SUPER_ADMIN', 'MODERATOR', 'FINANCE_OFFICER', 'SUPPORT_TEAM', 'RECRUITER', 'EVENT_OFFICER', 'JOURNALIST_ADMIN', 'SYSTEM_ADMIN', 'MARKETING_ADMIN', 'ANALYTICS_ADMIN') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    failed_login_attempts INT DEFAULT 0,
    account_locked_until TIMESTAMP,
    password_changed_at TIMESTAMP,
    session_timeout_minutes INT DEFAULT 60,
    allowed_ip_addresses TEXT,
    department VARCHAR(100),
    employee_id VARCHAR(50),
    supervisor_id BIGINT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    INDEX idx_admin_username (username),
    INDEX idx_admin_email (email),
    INDEX idx_admin_role (admin_role),
    INDEX idx_admin_active (is_active)
);

-- Create admin_permissions table
CREATE TABLE admin_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id BIGINT NOT NULL,
    permission ENUM('VIEW_USERS', 'EDIT_USERS', 'DELETE_USERS', 'VERIFY_USERS', 'BLOCK_USERS', 'ASSIGN_ROLES', 'VIEW_POSTS', 'MODERATE_POSTS', 'DELETE_POSTS', 'APPROVE_CONTENT', 'FLAG_CONTENT', 'MODERATE_COMMENTS', 'MODERATE_VIDEOS', 'MODERATE_NEWS', 'VIEW_TRANSACTIONS', 'APPROVE_WITHDRAWALS', 'APPROVE_DEPOSITS', 'MANAGE_COMMISSIONS', 'VIEW_FINANCIAL_REPORTS', 'CONFIGURE_FEES', 'RESOLVE_DISPUTES', 'VIEW_EVENTS', 'APPROVE_EVENTS', 'REJECT_EVENTS', 'MANAGE_TICKETS', 'VALIDATE_QR_CODES', 'VIEW_EVENT_REPORTS', 'VIEW_ADS', 'APPROVE_ADS', 'REJECT_ADS', 'MANAGE_CAMPAIGNS', 'VIEW_AD_ANALYTICS', 'CONFIGURE_AD_RATES', 'VIEW_JOBS', 'APPROVE_JOBS', 'REJECT_JOBS', 'MANAGE_APPLICATIONS', 'SCHEDULE_INTERVIEWS', 'RANK_APPLICANTS', 'MANAGE_ADMINS', 'CONFIGURE_SYSTEM', 'VIEW_AUDIT_LOGS', 'MANAGE_BACKUPS', 'CONFIGURE_APIS', 'SEND_NOTIFICATIONS', 'MANAGE_SETTINGS', 'VIEW_TICKETS', 'RESPOND_TO_TICKETS', 'ESCALATE_TICKETS', 'MANAGE_REPORTS', 'BAN_USERS', 'VIEW_ANALYTICS', 'GENERATE_REPORTS', 'EXPORT_DATA', 'VIEW_STATISTICS', 'MANAGE_SECURITY', 'VIEW_LOGIN_LOGS', 'CONFIGURE_2FA', 'MANAGE_IP_RESTRICTIONS', 'SUPER_ADMIN_ACCESS', 'EMERGENCY_ACCESS', 'SYSTEM_MAINTENANCE') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_permission (admin_user_id, permission)
);

-- Create admin_activity_logs table
CREATE TABLE admin_activity_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id BIGINT NOT NULL,
    action_type ENUM('LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'PASSWORD_CHANGED', 'TWO_FACTOR_ENABLED', 'TWO_FACTOR_DISABLED', 'USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'USER_BLOCKED', 'USER_UNBLOCKED', 'USER_VERIFIED', 'USER_UNVERIFIED', 'USER_ROLE_ASSIGNED', 'POST_APPROVED', 'POST_REJECTED', 'POST_DELETED', 'POST_FLAGGED', 'COMMENT_DELETED', 'VIDEO_APPROVED', 'VIDEO_REJECTED', 'NEWS_APPROVED', 'NEWS_REJECTED', 'TRANSACTION_APPROVED', 'TRANSACTION_REJECTED', 'WITHDRAWAL_APPROVED', 'WITHDRAWAL_REJECTED', 'DEPOSIT_APPROVED', 'COMMISSION_UPDATED', 'FEE_CONFIGURED', 'DISPUTE_RESOLVED', 'EVENT_APPROVED', 'EVENT_REJECTED', 'TICKET_VALIDATED', 'EVENT_CANCELLED', 'AD_APPROVED', 'AD_REJECTED', 'CAMPAIGN_CREATED', 'AD_RATE_UPDATED', 'JOB_APPROVED', 'JOB_REJECTED', 'APPLICATION_REVIEWED', 'INTERVIEW_SCHEDULED', 'CANDIDATE_RANKED', 'ADMIN_CREATED', 'ADMIN_UPDATED', 'ADMIN_DELETED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'SYSTEM_CONFIGURED', 'BACKUP_CREATED', 'BACKUP_RESTORED', 'API_CONFIGURED', 'NOTIFICATION_SENT', 'TICKET_RESPONDED', 'TICKET_ESCALATED', 'TICKET_CLOSED', 'REPORT_REVIEWED', 'USER_BANNED', 'IP_RESTRICTED', 'SECURITY_ALERT', 'AUDIT_LOG_VIEWED', 'EMERGENCY_ACCESS_USED', 'DATA_EXPORTED', 'REPORT_GENERATED', 'ANALYTICS_VIEWED', 'SYSTEM_MAINTENANCE', 'DATABASE_UPDATED', 'CACHE_CLEARED', 'SERVICE_RESTARTED') NOT NULL,
    action_description VARCHAR(500) NOT NULL,
    target_entity_type VARCHAR(50),
    target_entity_id BIGINT,
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    severity_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    execution_time_ms BIGINT,
    additional_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_activity_admin (admin_user_id),
    INDEX idx_activity_type (action_type),
    INDEX idx_activity_date (created_at),
    INDEX idx_activity_severity (severity_level)
);

-- Create job_postings table
CREATE TABLE job_postings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    employer_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    responsibilities TEXT,
    benefits TEXT,
    job_type ENUM('TECHNOLOGY', 'HEALTHCARE', 'EDUCATION', 'FINANCE', 'MARKETING', 'SALES', 'ENGINEERING', 'CONSTRUCTION', 'AGRICULTURE', 'TOURISM', 'HOSPITALITY', 'TRANSPORTATION', 'MANUFACTURING', 'RETAIL', 'GOVERNMENT', 'NGO', 'CONSULTING', 'LEGAL', 'MEDIA', 'ARTS', 'SPORTS', 'SECURITY', 'CLEANING', 'FOOD_SERVICE', 'CUSTOMER_SERVICE', 'HUMAN_RESOURCES', 'ADMINISTRATION', 'RESEARCH', 'OTHER'),
    employment_type ENUM('FULL_TIME', 'PART_TIME', 'CONTRACT', 'TEMPORARY', 'INTERNSHIP', 'FREELANCE', 'VOLUNTEER', 'REMOTE', 'HYBRID'),
    experience_level ENUM('ENTRY_LEVEL', 'JUNIOR', 'MID_LEVEL', 'SENIOR', 'LEAD', 'MANAGER', 'DIRECTOR', 'EXECUTIVE', 'INTERN', 'FRESH_GRADUATE'),
    company_name VARCHAR(255) NOT NULL,
    company_logo_url VARCHAR(500),
    company_description TEXT,
    location VARCHAR(255) NOT NULL,
    remote_work_allowed BOOLEAN DEFAULT FALSE,
    salary_min DECIMAL(15,2),
    salary_max DECIMAL(15,2),
    salary_currency VARCHAR(10) DEFAULT 'TSH',
    salary_period VARCHAR(20) DEFAULT 'MONTHLY',
    application_deadline TIMESTAMP,
    start_date TIMESTAMP,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    application_instructions TEXT,
    min_age INT,
    max_age INT,
    gender_preference VARCHAR(10) DEFAULT 'ANY',
    education_level VARCHAR(100),
    language_requirements VARCHAR(255),
    status ENUM('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'PUBLISHED', 'PAUSED', 'CLOSED', 'EXPIRED', 'REJECTED', 'FILLED') DEFAULT 'DRAFT',
    is_featured BOOLEAN DEFAULT FALSE,
    is_urgent BOOLEAN DEFAULT FALSE,
    views_count BIGINT DEFAULT 0,
    applications_count BIGINT DEFAULT 0,
    max_applications INT,
    auto_close_when_filled BOOLEAN DEFAULT TRUE,
    application_fee DECIMAL(10,2) DEFAULT 0.00,
    posting_fee DECIMAL(10,2) DEFAULT 0.00,
    featured_fee DECIMAL(10,2) DEFAULT 0.00,
    rejection_reason TEXT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_job_employer (employer_id),
    INDEX idx_job_type (job_type),
    INDEX idx_job_status (status),
    INDEX idx_job_location (location),
    INDEX idx_job_deadline (application_deadline)
);

-- Create job_applications table
CREATE TABLE job_applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_posting_id BIGINT NOT NULL,
    applicant_id BIGINT NOT NULL,
    cover_letter TEXT,
    resume_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    expected_salary DECIMAL(15,2),
    available_start_date TIMESTAMP,
    years_of_experience INT,
    education_level VARCHAR(100),
    skills TEXT,
    certifications TEXT,
    languages_spoken TEXT,
    references TEXT,
    additional_documents TEXT,
    questionnaire_answers TEXT,
    status ENUM('SUBMITTED', 'UNDER_REVIEW', 'SHORTLISTED', 'INTERVIEW_SCHEDULED', 'INTERVIEWED', 'SECOND_INTERVIEW', 'REFERENCE_CHECK', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'ON_HOLD') DEFAULT 'SUBMITTED',
    ai_score DECIMAL(5,2),
    admin_score DECIMAL(5,2),
    ranking_position INT,
    is_shortlisted BOOLEAN DEFAULT FALSE,
    shortlisted_at TIMESTAMP,
    shortlisted_by BIGINT,
    interview_scheduled BOOLEAN DEFAULT FALSE,
    interview_date TIMESTAMP,
    interview_type VARCHAR(20),
    interview_location VARCHAR(255),
    interview_notes TEXT,
    interviewer_id BIGINT,
    feedback TEXT,
    rejection_reason TEXT,
    application_fee_paid DECIMAL(10,2) DEFAULT 0.00,
    payment_reference VARCHAR(100),
    viewed_by_employer BOOLEAN DEFAULT FALSE,
    viewed_at TIMESTAMP,
    response_deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_posting_id) REFERENCES job_postings(id) ON DELETE CASCADE,
    FOREIGN KEY (applicant_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_application_job (job_posting_id),
    INDEX idx_application_applicant (applicant_id),
    INDEX idx_application_status (status),
    INDEX idx_application_shortlisted (is_shortlisted)
);

-- Create support_tickets table
CREATE TABLE support_tickets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    assigned_to BIGINT,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category ENUM('TECHNICAL_ISSUE', 'ACCOUNT_PROBLEM', 'PAYMENT_ISSUE', 'PROPAY_WALLET', 'TRANSACTION_DISPUTE', 'LOGIN_PROBLEM', 'APP_BUG', 'FEATURE_REQUEST', 'CONTENT_REPORT', 'HARASSMENT_REPORT', 'SPAM_REPORT', 'FRAUD_REPORT', 'EVENT_ISSUE', 'TICKET_PROBLEM', 'JOB_APPLICATION', 'LIVE_STREAM_ISSUE', 'GIFT_PROBLEM', 'VERIFICATION_REQUEST', 'AGENT_SUPPORT', 'MERCHANT_SUPPORT', 'GENERAL_INQUIRY', 'FEEDBACK', 'COMPLAINT', 'OTHER'),
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL') DEFAULT 'MEDIUM',
    status ENUM('OPEN', 'IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'WAITING_FOR_APPROVAL', 'ESCALATED', 'RESOLVED', 'CLOSED', 'REOPENED', 'CANCELLED') DEFAULT 'OPEN',
    user_email VARCHAR(255),
    user_phone VARCHAR(20),
    device_info VARCHAR(255),
    app_version VARCHAR(50),
    os_version VARCHAR(50),
    error_logs TEXT,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    related_entity_type VARCHAR(50),
    related_entity_id BIGINT,
    is_escalated BOOLEAN DEFAULT FALSE,
    escalated_to BIGINT,
    escalated_at TIMESTAMP,
    escalation_reason TEXT,
    resolution TEXT,
    resolved_by BIGINT,
    resolved_at TIMESTAMP,
    first_response_at TIMESTAMP,
    last_response_at TIMESTAMP,
    response_time_hours INT,
    resolution_time_hours INT,
    satisfaction_rating INT,
    satisfaction_feedback TEXT,
    internal_notes TEXT,
    tags TEXT,
    auto_response_sent BOOLEAN DEFAULT FALSE,
    follow_up_required BOOLEAN DEFAULT FALSE,
    follow_up_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_ticket_number (ticket_number),
    INDEX idx_ticket_user (user_id),
    INDEX idx_ticket_status (status),
    INDEX idx_ticket_priority (priority),
    INDEX idx_ticket_category (category)
);

-- Create digital_invitations table
CREATE TABLE digital_invitations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    creator_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    invitation_type ENUM('WEDDING', 'BIRTHDAY', 'GRADUATION', 'BABY_SHOWER', 'ANNIVERSARY', 'ENGAGEMENT', 'HOUSEWARMING', 'RETIREMENT', 'FUNERAL', 'MEMORIAL', 'BUSINESS_EVENT', 'CONFERENCE', 'WORKSHOP', 'SEMINAR', 'PARTY', 'DINNER', 'LUNCH', 'MEETING', 'CELEBRATION', 'HOLIDAY', 'RELIGIOUS', 'CULTURAL', 'SPORTS', 'CHARITY', 'FUNDRAISER', 'REUNION', 'FESTIVAL', 'CONCERT', 'EXHIBITION', 'LAUNCH', 'OTHER'),
    template_id VARCHAR(100),
    background_image_url VARCHAR(500),
    background_color VARCHAR(10) DEFAULT '#FFFFFF',
    text_color VARCHAR(10) DEFAULT '#000000',
    accent_color VARCHAR(10) DEFAULT '#007AFF',
    font_family VARCHAR(50) DEFAULT 'Arial',
    font_size INT DEFAULT 16,
    event_name VARCHAR(255),
    event_date TIMESTAMP,
    event_time VARCHAR(50),
    event_location VARCHAR(255),
    event_address TEXT,
    dress_code VARCHAR(100),
    special_instructions TEXT,
    rsvp_required BOOLEAN DEFAULT FALSE,
    rsvp_deadline TIMESTAMP,
    rsvp_contact VARCHAR(255),
    max_guests INT,
    plus_one_allowed BOOLEAN DEFAULT FALSE,
    gift_registry_info TEXT,
    custom_message TEXT,
    host_name VARCHAR(255),
    host_contact VARCHAR(255),
    invitation_url VARCHAR(500) UNIQUE,
    qr_code_url VARCHAR(500),
    is_public BOOLEAN DEFAULT FALSE,
    password_protected BOOLEAN DEFAULT FALSE,
    access_password VARCHAR(255),
    views_count BIGINT DEFAULT 0,
    rsvp_yes_count INT DEFAULT 0,
    rsvp_no_count INT DEFAULT 0,
    rsvp_maybe_count INT DEFAULT 0,
    total_sent INT DEFAULT 0,
    sms_sent INT DEFAULT 0,
    email_sent INT DEFAULT 0,
    whatsapp_sent INT DEFAULT 0,
    creation_fee DECIMAL(10,2) DEFAULT 0.00,
    sms_fee_per_message DECIMAL(5,2) DEFAULT 50.00,
    total_fees_paid DECIMAL(15,2) DEFAULT 0.00,
    payment_reference VARCHAR(100),
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_invitation_creator (creator_id),
    INDEX idx_invitation_type (invitation_type),
    INDEX idx_invitation_url (invitation_url)
);

-- =============================================
-- ADVANCED MONETIZATION TABLES
-- =============================================

-- Create earning_rates table
CREATE TABLE earning_rates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('LIKE', 'DOWNLOAD', 'GIFT', 'REPOST', 'INVITE_FRIEND', 'TICKET_SALE', 'DONATION', 'VERIFICATION_BONUS', 'AGENT_COMMISSION', 'MERCHANT_PAYMENT', 'CASHBACK', 'TASK_BONUS') NOT NULL UNIQUE,
    rate DECIMAL(10,4) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    minimum_threshold DECIMAL(10,4),
    maximum_per_day DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    INDEX idx_earning_type (type),
    INDEX idx_earning_active (is_active)
);

-- Create user_earnings table
CREATE TABLE user_earnings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    earning_type ENUM('LIKE', 'DOWNLOAD', 'GIFT', 'REPOST', 'INVITE_FRIEND', 'TICKET_SALE', 'DONATION', 'VERIFICATION_BONUS', 'AGENT_COMMISSION', 'MERCHANT_PAYMENT', 'CASHBACK', 'TASK_BONUS') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    source_id BIGINT,
    source_type VARCHAR(50),
    description TEXT,
    quantity INT,
    rate_used DECIMAL(10,4),
    status ENUM('PENDING', 'PROCESSED', 'PAID', 'CANCELLED', 'FAILED') DEFAULT 'PENDING',
    processed_at TIMESTAMP,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_earning_user (user_id),
    INDEX idx_earning_type (earning_type),
    INDEX idx_earning_status (status),
    INDEX idx_earning_date (created_at)
);

-- Create gift_types table
CREATE TABLE gift_types (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    icon_url VARCHAR(500),
    animation_url VARCHAR(500),
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    recipient_percentage DECIMAL(5,2) DEFAULT 80.00,
    platform_percentage DECIMAL(5,2) DEFAULT 20.00,
    category ENUM('BASIC', 'FLOWERS', 'JEWELRY', 'VEHICLES', 'ANIMALS', 'SPECIAL', 'SEASONAL') NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    minimum_level INT DEFAULT 1,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    INDEX idx_gift_category (category),
    INDEX idx_gift_active (is_active),
    INDEX idx_gift_price (price)
);

-- Create gift_transactions table
CREATE TABLE gift_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gift_type_id BIGINT NOT NULL,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    quantity INT DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    recipient_amount DECIMAL(10,2) NOT NULL,
    platform_amount DECIMAL(10,2) NOT NULL,
    source_type VARCHAR(50),
    source_id BIGINT,
    message TEXT,
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED') DEFAULT 'COMPLETED',
    transaction_reference VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (gift_type_id) REFERENCES gift_types(id),
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_gift_trans_sender (sender_id),
    INDEX idx_gift_trans_recipient (recipient_id),
    INDEX idx_gift_trans_date (created_at)
);

-- Create tasks table
CREATE TABLE tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    task_type ENUM('LIKE_POSTS', 'COMMENT_POSTS', 'SHARE_POSTS', 'INVITE_FRIENDS', 'COMPLETE_PROFILE', 'VERIFY_EMAIL', 'VERIFY_PHONE', 'FOLLOW_USERS', 'CREATE_POSTS', 'WATCH_VIDEOS', 'ATTEND_EVENTS', 'BUY_TICKETS', 'USE_PROPAY', 'SEND_GIFTS', 'DAILY_LOGIN', 'WEEKLY_ACTIVE', 'MONTHLY_CHALLENGE') NOT NULL,
    target_count INT NOT NULL,
    reward_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    is_active BOOLEAN DEFAULT TRUE,
    is_daily BOOLEAN DEFAULT FALSE,
    is_weekly BOOLEAN DEFAULT FALSE,
    is_monthly BOOLEAN DEFAULT FALSE,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    max_completions INT,
    minimum_level INT DEFAULT 1,
    icon_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    INDEX idx_task_type (task_type),
    INDEX idx_task_active (is_active),
    INDEX idx_task_dates (start_date, end_date)
);

-- Create user_task_completions table
CREATE TABLE user_task_completions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    task_id BIGINT NOT NULL,
    current_count INT DEFAULT 0,
    target_count INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP,
    reward_claimed BOOLEAN DEFAULT FALSE,
    reward_claimed_at TIMESTAMP,
    reward_amount DECIMAL(10,2),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    INDEX idx_task_completion_user (user_id),
    INDEX idx_task_completion_task (task_id),
    INDEX idx_task_completion_status (is_completed),
    UNIQUE KEY unique_user_task_daily (user_id, task_id, DATE(started_at))
);

-- Create donation_campaigns table
CREATE TABLE donation_campaigns (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    creator_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    target_amount DECIMAL(12,2) NOT NULL,
    current_amount DECIMAL(12,2) DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'TZS',
    category ENUM('MEDICAL', 'EDUCATION', 'EMERGENCY', 'COMMUNITY', 'BUSINESS', 'CHARITY', 'DISASTER', 'FAMILY', 'SPORTS', 'ARTS') NOT NULL,
    image_url VARCHAR(500),
    video_url VARCHAR(500),
    beneficiary_name VARCHAR(255),
    beneficiary_contact VARCHAR(255),
    location VARCHAR(255),
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'REJECTED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'SUSPENDED') DEFAULT 'PENDING',
    platform_fee_percentage DECIMAL(5,2) DEFAULT 5.00,
    is_featured BOOLEAN DEFAULT FALSE,
    is_urgent BOOLEAN DEFAULT FALSE,
    verification_documents TEXT,
    admin_notes TEXT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_donation_creator (creator_id),
    INDEX idx_donation_category (category),
    INDEX idx_donation_status (status),
    INDEX idx_donation_dates (start_date, end_date)
);

-- Create donation_transactions table
CREATE TABLE donation_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    campaign_id BIGINT NOT NULL,
    donor_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL,
    net_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    payment_method VARCHAR(50),
    transaction_reference VARCHAR(100),
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED') DEFAULT 'COMPLETED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES donation_campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_donation_trans_campaign (campaign_id),
    INDEX idx_donation_trans_donor (donor_id),
    INDEX idx_donation_trans_date (created_at)
);

-- Create cashback_rules table
CREATE TABLE cashback_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    activity_type ENUM('PROPAY_TRANSACTION', 'TICKET_PURCHASE', 'GIFT_SENDING', 'EVENT_ATTENDANCE', 'MERCHANT_PAYMENT', 'BILL_PAYMENT', 'MONEY_TRANSFER', 'MOBILE_RECHARGE', 'SUBSCRIPTION', 'DONATION') NOT NULL,
    cashback_percentage DECIMAL(5,2) NOT NULL,
    minimum_amount DECIMAL(10,2),
    maximum_cashback DECIMAL(10,2),
    minimum_user_level INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    max_uses_per_user INT,
    max_uses_per_day INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    INDEX idx_cashback_activity (activity_type),
    INDEX idx_cashback_active (is_active),
    INDEX idx_cashback_dates (start_date, end_date)
);

-- Create cashback_transactions table
CREATE TABLE cashback_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    cashback_rule_id BIGINT NOT NULL,
    original_transaction_id BIGINT,
    original_amount DECIMAL(10,2) NOT NULL,
    cashback_amount DECIMAL(10,2) NOT NULL,
    cashback_percentage DECIMAL(5,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'TZS',
    description TEXT,
    status ENUM('PENDING', 'PROCESSED', 'EXPIRED', 'CANCELLED') DEFAULT 'PENDING',
    processed_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (cashback_rule_id) REFERENCES cashback_rules(id),
    INDEX idx_cashback_trans_user (user_id),
    INDEX idx_cashback_trans_rule (cashback_rule_id),
    INDEX idx_cashback_trans_status (status),
    INDEX idx_cashback_trans_date (created_at)
);

-- Create user_loyalty_levels table
CREATE TABLE user_loyalty_levels (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    current_level INT DEFAULT 1,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    total_cashback_earned DECIMAL(10,2) DEFAULT 0.00,
    points_earned BIGINT DEFAULT 0,
    points_redeemed BIGINT DEFAULT 0,
    level_upgraded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_loyalty_level (current_level),
    INDEX idx_loyalty_spent (total_spent)
);

-- =============================================
-- INSERT SAMPLE MONETIZATION DATA
-- =============================================

-- Insert default earning rates
INSERT INTO earning_rates (type, rate, currency, description, minimum_threshold, maximum_per_day) VALUES
('LIKE', 0.5, 'TZS', 'Mapato kwa kila like', 1, 1000),
('DOWNLOAD', 0.25, 'TZS', 'Mapato kwa kila MB iliyopakuliwa', 1, 5000),
('GIFT', 80.0, '%', 'Asilimia ya zawadi anayopokea mtumiaji', 0, 50000),
('REPOST', 1.0, 'TZS', 'Mapato kwa kushare maudhui', 1, 2000),
('INVITE_FRIEND', 100.0, 'TZS', 'Bonasi ya kualika rafiki', 1, 1000),
('TICKET_SALE', 5.0, '%', 'Asilimia ya mauzo ya tiketi', 0, 100000),
('DONATION', 2.0, '%', 'Asilimia ya michango', 0, 10000),
('AGENT_COMMISSION', 1.0, '%', 'Kamisheni ya wakala', 0, 50000),
('MERCHANT_PAYMENT', 1.5, '%', 'Kamisheni ya malipo ya biashara', 0, 100000),
('CASHBACK', 2.0, '%', 'Mapato ya cashback', 0, 5000),
('TASK_BONUS', 50.0, 'TZS', 'Bonasi ya kazi', 1, 2000);

-- Insert default gift types
INSERT INTO gift_types (name, display_name, price, category, description, recipient_percentage, platform_percentage) VALUES
('coin', 'Sarafu', 50, 'BASIC', 'Zawadi ya msingi ya sarafu', 80.00, 20.00),
('heart', 'Moyo', 100, 'BASIC', 'Zawadi ya upendo', 80.00, 20.00),
('thumbs_up', 'Kidole Juu', 75, 'BASIC', 'Zawadi ya kupongeza', 80.00, 20.00),
('rose', 'Waridi', 200, 'FLOWERS', 'Waridi la upendo', 80.00, 20.00),
('tulip', 'Tulip', 150, 'FLOWERS', 'Ua la tulip', 80.00, 20.00),
('sunflower', 'Alizeti', 180, 'FLOWERS', 'Ua la alizeti', 80.00, 20.00),
('diamond', 'Almasi', 1000, 'JEWELRY', 'Almasi ya thamani', 85.00, 15.00),
('ring', 'Pete', 800, 'JEWELRY', 'Pete ya dhahabu', 85.00, 15.00),
('necklace', 'Mkufu', 600, 'JEWELRY', 'Mkufu wa dhahabu', 85.00, 15.00),
('car', 'Gari', 5000, 'VEHICLES', 'Gari la kifahari', 90.00, 10.00),
('bike', 'Pikipiki', 2000, 'VEHICLES', 'Pikipiki ya haraka', 85.00, 15.00),
('plane', 'Ndege', 10000, 'VEHICLES', 'Ndege ya kibinafsi', 90.00, 10.00),
('crown', 'Taji', 3000, 'SPECIAL', 'Taji la mfalme', 85.00, 15.00),
('trophy', 'Kombe', 1500, 'SPECIAL', 'Kombe la ushindi', 85.00, 15.00),
('star', 'Nyota', 500, 'SPECIAL', 'Nyota ya mwanga', 80.00, 20.00);

-- Insert default tasks
INSERT INTO tasks (title, description, task_type, target_count, reward_amount, is_daily, is_active) VALUES
('Penda Machapisho 50', 'Penda machapisho 50 kwa siku', 'LIKE_POSTS', 50, 500, TRUE, TRUE),
('Andika Maoni 10', 'Andika maoni 10 kwa machapisho', 'COMMENT_POSTS', 10, 300, TRUE, TRUE),
('Shiriki Machapisho 5', 'Shiriki machapisho 5 kwa siku', 'SHARE_POSTS', 5, 200, TRUE, TRUE),
('Alika Marafiki 3', 'Alika marafiki 3 wapya', 'INVITE_FRIENDS', 3, 1000, FALSE, TRUE),
('Kamilisha Wasifu', 'Kamilisha taarifa za wasifu wako', 'COMPLETE_PROFILE', 1, 500, FALSE, TRUE),
('Thibitisha Barua Pepe', 'Thibitisha barua pepe yako', 'VERIFY_EMAIL', 1, 200, FALSE, TRUE),
('Thibitisha Namba ya Simu', 'Thibitisha namba yako ya simu', 'VERIFY_PHONE', 1, 300, FALSE, TRUE),
('Fuata Watumiaji 20', 'Fuata watumiaji 20 wapya', 'FOLLOW_USERS', 20, 400, FALSE, TRUE),
('Unda Machapisho 3', 'Unda machapisho 3 kwa siku', 'CREATE_POSTS', 3, 300, TRUE, TRUE),
('Tazama Video 10', 'Tazama video 10 kwa siku', 'WATCH_VIDEOS', 10, 200, TRUE, TRUE),
('Ingia Kila Siku', 'Ingia kwenye app kila siku kwa wiki 7', 'DAILY_LOGIN', 7, 1000, FALSE, TRUE),
('Tumia ProPay', 'Fanya miamala 5 kwa ProPay', 'USE_PROPAY', 5, 800, FALSE, TRUE),
('Tuma Zawadi 3', 'Tuma zawadi 3 kwa watumiaji wengine', 'SEND_GIFTS', 3, 600, FALSE, TRUE);

-- Insert default cashback rules
INSERT INTO cashback_rules (name, description, activity_type, cashback_percentage, minimum_amount, maximum_cashback, is_active) VALUES
('ProPay Cashback', 'Cashback kwa miamala ya ProPay', 'PROPAY_TRANSACTION', 2.0, 1000, 500, TRUE),
('Ticket Cashback', 'Cashback kwa ununuzi wa tiketi', 'TICKET_PURCHASE', 5.0, 5000, 1000, TRUE),
('Gift Cashback', 'Cashback kwa kutuma zawadi', 'GIFT_SENDING', 3.0, 500, 300, TRUE),
('Merchant Cashback', 'Cashback kwa malipo ya biashara', 'MERCHANT_PAYMENT', 1.5, 2000, 800, TRUE),
('Bill Payment Cashback', 'Cashback kwa malipo ya bili', 'BILL_PAYMENT', 1.0, 1000, 200, TRUE),
('Mobile Recharge Cashback', 'Cashback kwa kujaza mitandao', 'MOBILE_RECHARGE', 2.5, 500, 150, TRUE),
('Donation Cashback', 'Cashback kwa michango', 'DONATION', 1.0, 1000, 100, TRUE);

SET FOREIGN_KEY_CHECKS = 1;
