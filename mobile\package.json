{"name": "ProChatMobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-vector-icons": "^10.0.3", "axios": "^1.6.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-image-picker": "^7.0.3", "react-native-video": "^5.2.1", "react-native-webrtc": "^118.0.1", "socket.io-client": "^4.7.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}