server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: prochat-backend

  datasource:
    url: ***************************************************************************************************
    username: root
    password: Ram$0101
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# JWT Configuration
jwt:
  secret: ProChatSecretKeyForJWTTokenGeneration2024
  expiration: 86400000 # 24 hours in milliseconds

# AWS S3 Configuration
aws:
  s3:
    bucket-name: prochat-media-storage
    region: us-east-1
    access-key: ${AWS_ACCESS_KEY:}
    secret-key: ${AWS_SECRET_KEY:}

# ProPay Configuration
propay:
  transaction:
    fee-percentage: 0.02 # 2% transaction fee
  agent:
    commission-percentage: 0.01 # 1% agent commission

# Logging
logging:
  level:
    com.prochat: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/prochat.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# CORS Configuration
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:19006
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.prochat: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.prochat: WARN
