import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Configure axios defaults
  useEffect(() => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // Verify token validity
      verifyToken();
    } else {
      setLoading(false);
    }
  }, []);

  const verifyToken = async () => {
    try {
      const response = await axios.get('/api/admin/auth/verify');
      setUser(response.data);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Token verification failed:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/admin/auth/login', credentials);
      
      const { token, user: userData, permissions } = response.data;
      
      // Store token
      localStorage.setItem('admin_token', token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Set user data
      const userWithPermissions = { ...userData, permissions };
      setUser(userWithPermissions);
      setIsAuthenticated(true);
      
      toast.success(`Karibu, ${userData.firstName}!`);
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Imeshindikana kuingia. Jaribu tena.';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('admin_token');
    delete axios.defaults.headers.common['Authorization'];
    setUser(null);
    setIsAuthenticated(false);
    toast.success('Umetoka kikamilifu');
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await axios.put('/api/admin/profile', profileData);
      setUser(response.data);
      toast.success('Profaili imesasishwa');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Imeshindikana kusasisha profaili';
      toast.error(message);
      return { success: false, message };
    }
  };

  const changePassword = async (passwordData) => {
    try {
      await axios.put('/api/admin/auth/change-password', passwordData);
      toast.success('Nywila imebadilishwa kikamilifu');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Imeshindikana kubadilisha nywila';
      toast.error(message);
      return { success: false, message };
    }
  };

  const enable2FA = async () => {
    try {
      const response = await axios.post('/api/admin/auth/enable-2fa');
      toast.success('2FA imewashwa kikamilifu');
      return { success: true, data: response.data };
    } catch (error) {
      const message = error.response?.data?.message || 'Imeshindikana kuwasha 2FA';
      toast.error(message);
      return { success: false, message };
    }
  };

  const disable2FA = async (code) => {
    try {
      await axios.post('/api/admin/auth/disable-2fa', { code });
      setUser(prev => ({ ...prev, twoFactorEnabled: false }));
      toast.success('2FA imezimwa kikamilifu');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Imeshindikana kuzima 2FA';
      toast.error(message);
      return { success: false, message };
    }
  };

  const hasPermission = (permission) => {
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission) || user.permissions.includes('SUPER_ADMIN_ACCESS');
  };

  const hasRole = (role) => {
    if (!user) return false;
    return user.adminRole === role || user.adminRole === 'SUPER_ADMIN';
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateProfile,
    changePassword,
    enable2FA,
    disable2FA,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
