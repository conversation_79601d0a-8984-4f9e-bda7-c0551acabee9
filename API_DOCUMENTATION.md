# ProChat API Documentation

## 🚀 Overview

ProChat API ni RESTful API iliyojengwa kwa Spring Boot inayotoa huduma kamili za mitandao ya kijamii na miamala ya kifedha.

## 🔗 Base URLs

- **Production**: `https://api.prochat.co.tz`
- **Development**: `http://localhost:8080/api`
- **Swagger UI**: `https://api.prochat.co.tz/swagger-ui.html`

## 🔐 Authentication

### JWT Token Authentication
```http
Authorization: Bearer <your_jwt_token>
```

### Admin Authentication
```http
Authorization: Bearer <admin_jwt_token>
X-Admin-Role: SUPER_ADMIN
```

## 📱 Mobile App Endpoints

### Authentication
```http
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh-token
POST /api/auth/forgot-password
POST /api/auth/reset-password
POST /api/auth/verify-email
POST /api/auth/resend-verification
```

### User Management
```http
GET    /api/users/profile
PUT    /api/users/profile
POST   /api/users/upload-avatar
GET    /api/users/{id}
POST   /api/users/follow/{id}
DELETE /api/users/unfollow/{id}
GET    /api/users/followers
GET    /api/users/following
POST   /api/users/block/{id}
DELETE /api/users/unblock/{id}
```

### Social Media Features
```http
# Posts
GET    /api/posts
POST   /api/posts
GET    /api/posts/{id}
PUT    /api/posts/{id}
DELETE /api/posts/{id}
POST   /api/posts/{id}/like
DELETE /api/posts/{id}/unlike
POST   /api/posts/{id}/comment
GET    /api/posts/{id}/comments

# Stories
GET    /api/stories
POST   /api/stories
DELETE /api/stories/{id}

# Short Videos
GET    /api/videos
POST   /api/videos
GET    /api/videos/{id}
POST   /api/videos/{id}/like
POST   /api/videos/{id}/comment
```

### Chat & Messaging
```http
GET    /api/conversations
POST   /api/conversations
GET    /api/conversations/{id}/messages
POST   /api/conversations/{id}/messages
PUT    /api/messages/{id}/read
DELETE /api/messages/{id}
```

### ProPay Wallet
```http
GET    /api/wallet/balance
POST   /api/wallet/deposit
POST   /api/wallet/withdraw
POST   /api/wallet/transfer
GET    /api/wallet/transactions
GET    /api/wallet/transaction/{id}
POST   /api/wallet/request-money
POST   /api/wallet/pay-bill
```

### Events
```http
GET    /api/events
GET    /api/events/{id}
POST   /api/events/{id}/buy-ticket
GET    /api/events/my-tickets
POST   /api/events/validate-ticket
```

### Jobs & Tenders
```http
GET    /api/jobs
GET    /api/jobs/{id}
POST   /api/jobs/{id}/apply
GET    /api/jobs/my-applications
```

### Digital Invitations
```http
GET    /api/invitations
POST   /api/invitations
GET    /api/invitations/{id}
PUT    /api/invitations/{id}
POST   /api/invitations/{id}/send
POST   /api/invitations/{id}/rsvp
```

## 🖥️ Admin Panel Endpoints

### Admin Authentication
```http
POST /api/admin/auth/login
POST /api/admin/auth/logout
POST /api/admin/auth/refresh-token
POST /api/admin/auth/enable-2fa
POST /api/admin/auth/verify-2fa
```

### Dashboard & Analytics
```http
GET /api/admin/dashboard/stats
GET /api/admin/analytics/users
GET /api/admin/analytics/revenue
GET /api/admin/analytics/content
GET /api/admin/analytics/events
```

### User Management
```http
GET    /api/admin/users
GET    /api/admin/users/{id}
PUT    /api/admin/users/{id}
POST   /api/admin/users/{id}/block
POST   /api/admin/users/{id}/unblock
POST   /api/admin/users/{id}/verify
DELETE /api/admin/users/{id}
GET    /api/admin/users/{id}/activity
```

### Content Moderation
```http
GET    /api/admin/content/posts
GET    /api/admin/content/videos
GET    /api/admin/content/comments
POST   /api/admin/content/{id}/approve
POST   /api/admin/content/{id}/reject
DELETE /api/admin/content/{id}
GET    /api/admin/content/reports
```

### Financial Management
```http
GET    /api/admin/finance/transactions
GET    /api/admin/finance/withdrawals
POST   /api/admin/finance/withdrawal/{id}/approve
POST   /api/admin/finance/withdrawal/{id}/reject
GET    /api/admin/finance/deposits
GET    /api/admin/finance/commissions
GET    /api/admin/finance/reports
```

### Event Management
```http
GET    /api/admin/events
POST   /api/admin/events/{id}/approve
POST   /api/admin/events/{id}/reject
GET    /api/admin/events/{id}/tickets
GET    /api/admin/events/sales-report
```

### Job Management
```http
GET    /api/admin/jobs
POST   /api/admin/jobs/{id}/approve
POST   /api/admin/jobs/{id}/reject
GET    /api/admin/jobs/{id}/applications
POST   /api/admin/jobs/application/{id}/shortlist
```

### Support Tickets
```http
GET    /api/admin/support/tickets
GET    /api/admin/support/tickets/{id}
POST   /api/admin/support/tickets/{id}/respond
POST   /api/admin/support/tickets/{id}/assign
POST   /api/admin/support/tickets/{id}/close
```

### Admin Management
```http
GET    /api/admin/admins
POST   /api/admin/admins
GET    /api/admin/admins/{id}
PUT    /api/admin/admins/{id}
DELETE /api/admin/admins/{id}
GET    /api/admin/permissions
POST   /api/admin/admins/{id}/permissions
```

### System Settings
```http
GET    /api/admin/settings
PUT    /api/admin/settings
POST   /api/admin/settings/backup
GET    /api/admin/settings/logs
POST   /api/admin/settings/maintenance
```

## 🌐 Public Website Endpoints

### Public Content
```http
GET /api/public/news
GET /api/public/events
GET /api/public/jobs
GET /api/public/channels
```

### Digital Invitations (Public)
```http
GET    /api/public/invitations/{token}
POST   /api/public/invitations
POST   /api/public/invitations/{token}/rsvp
```

## 📊 Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error message"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### Pagination Response
```json
{
  "success": true,
  "data": {
    "content": [...],
    "page": 0,
    "size": 20,
    "totalElements": 100,
    "totalPages": 5,
    "first": true,
    "last": false
  }
}
```

## 🔒 Security Headers

### Required Headers
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer <token>
X-Requested-With: XMLHttpRequest
```

### Optional Headers
```http
X-Admin-Role: SUPER_ADMIN (for admin endpoints)
X-Device-ID: unique_device_identifier
X-App-Version: 1.0.0
X-Platform: android|ios|web
```

## 📈 Rate Limiting

- **General API**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **File Upload**: 5 requests per minute
- **Admin API**: 200 requests per minute

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Validation Error |
| 429 | Rate Limit Exceeded |
| 500 | Internal Server Error |

## 📱 WebSocket Endpoints

### Real-time Chat
```
ws://localhost:8080/ws/chat
wss://api.prochat.co.tz/ws/chat
```

### Live Notifications
```
ws://localhost:8080/ws/notifications
wss://api.prochat.co.tz/ws/notifications
```

### Live Streaming
```
ws://localhost:8080/ws/stream
wss://api.prochat.co.tz/ws/stream
```

## 🧪 Testing

### Postman Collection
Import the Postman collection: `ProChat_API.postman_collection.json`

### Test Credentials
```
Admin:
  Username: superadmin
  Password: ProChat2024!

Test User:
  Email: <EMAIL>
  Password: TestUser123!
```

## 📚 Additional Resources

- **Swagger UI**: `/swagger-ui.html`
- **API Docs**: `/api-docs`
- **Health Check**: `/health`
- **Metrics**: `/actuator/metrics`

## 🆘 Support

Kwa msaada wa API:
- Email: <EMAIL>
- Documentation: https://docs.prochat.co.tz
- Status Page: https://status.prochat.co.tz
