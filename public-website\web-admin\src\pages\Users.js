import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Paper,
  TextField,
  Button,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as Filter<PERSON><PERSON>,
  More<PERSON><PERSON> as MoreVertIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';

// Mock data
const mockUsers = [
  {
    id: 1,
    name: '<PERSON>',
    username: '@john<PERSON><PERSON>i',
    email: '<EMAIL>',
    phone: '+255123456789',
    status: 'ACTIVE',
    role: 'USER',
    walletBalance: 125000,
    joinDate: '2024-01-15',
    lastSeen: '2024-01-20 10:30',
    isVerified: true,
  },
  {
    id: 2,
    name: '<PERSON>',
    username: '@ma<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+255987654321',
    status: 'ACTIVE',
    role: 'AGENT',
    walletBalance: 75000,
    joinDate: '2024-01-10',
    lastSeen: '2024-01-20 09:15',
    isVerified: false,
  },
  {
    id: 3,
    name: 'Peter Mushi',
    username: '@petermushi',
    email: '<EMAIL>',
    phone: '+255456789123',
    status: 'SUSPENDED',
    role: 'USER',
    walletBalance: 0,
    joinDate: '2024-01-05',
    lastSeen: '2024-01-18 14:20',
    isVerified: true,
  },
];

export default function Users() {
  const [users, setUsers] = useState(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterRole, setFilterRole] = useState('ALL');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');

  const handleMenuClick = (event, user) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleDialogOpen = (type) => {
    setDialogType(type);
    setDialogOpen(true);
    handleMenuClose();
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setDialogType('');
    setSelectedUser(null);
  };

  const handleUserAction = (action) => {
    if (!selectedUser) return;

    switch (action) {
      case 'suspend':
        setUsers(users.map(user => 
          user.id === selectedUser.id 
            ? { ...user, status: 'SUSPENDED' }
            : user
        ));
        break;
      case 'activate':
        setUsers(users.map(user => 
          user.id === selectedUser.id 
            ? { ...user, status: 'ACTIVE' }
            : user
        ));
        break;
      case 'delete':
        setUsers(users.filter(user => user.id !== selectedUser.id));
        break;
      default:
        break;
    }
    handleDialogClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'SUSPENDED': return 'warning';
      case 'BANNED': return 'error';
      default: return 'default';
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'ADMIN': return 'error';
      case 'AGENT': return 'info';
      case 'MERCHANT': return 'warning';
      default: return 'default';
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'ALL' || user.status === filterStatus;
    const matchesRole = filterRole === 'ALL' || user.role === filterRole;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  const columns = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      renderCell: (params) => (
        <Avatar sx={{ width: 32, height: 32 }}>
          {params.row.name.charAt(0)}
        </Avatar>
      ),
      sortable: false,
    },
    {
      field: 'name',
      headerName: 'Jina',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {params.row.name}
            {params.row.isVerified && ' ✓'}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.username}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'email',
      headerName: 'Barua Pepe',
      width: 200,
    },
    {
      field: 'phone',
      headerName: 'Simu',
      width: 150,
    },
    {
      field: 'status',
      headerName: 'Hali',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'role',
      headerName: 'Jukumu',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getRoleColor(params.value)}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'walletBalance',
      headerName: 'Salio',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          TSH {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'joinDate',
      headerName: 'Tarehe ya Kujiunga',
      width: 150,
    },
    {
      field: 'actions',
      headerName: 'Vitendo',
      width: 80,
      renderCell: (params) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuClick(e, params.row)}
        >
          <MoreVertIcon />
        </IconButton>
      ),
      sortable: false,
    },
  ];

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        Watumiaji
      </Typography>

      {/* Filters and Search */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField
            placeholder="Tafuta watumiaji..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ minWidth: 300 }}
          />
          
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Hali</InputLabel>
            <Select
              value={filterStatus}
              label="Hali"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="ALL">Zote</MenuItem>
              <MenuItem value="ACTIVE">Hai</MenuItem>
              <MenuItem value="SUSPENDED">Imesimamishwa</MenuItem>
              <MenuItem value="BANNED">Imezuiliwa</MenuItem>
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Jukumu</InputLabel>
            <Select
              value={filterRole}
              label="Jukumu"
              onChange={(e) => setFilterRole(e.target.value)}
            >
              <MenuItem value="ALL">Zote</MenuItem>
              <MenuItem value="USER">Mtumiaji</MenuItem>
              <MenuItem value="AGENT">Wakala</MenuItem>
              <MenuItem value="MERCHANT">Mfanyabiashara</MenuItem>
              <MenuItem value="ADMIN">Msimamizi</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{ ml: 'auto' }}
          >
            Ongeza Mtumiaji
          </Button>
        </Box>
      </Paper>

      {/* Users Table */}
      <Paper sx={{ height: 600 }}>
        <DataGrid
          rows={filteredUsers}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10, 25, 50]}
          disableSelectionOnClick
          sx={{
            border: 'none',
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
            },
          }}
        />
      </Paper>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleDialogOpen('edit')}>
          <EditIcon sx={{ mr: 1 }} />
          Hariri
        </MenuItem>
        {selectedUser?.status === 'ACTIVE' ? (
          <MenuItem onClick={() => handleDialogOpen('suspend')}>
            <BlockIcon sx={{ mr: 1 }} />
            Simamisha
          </MenuItem>
        ) : (
          <MenuItem onClick={() => handleDialogOpen('activate')}>
            <EditIcon sx={{ mr: 1 }} />
            Washa
          </MenuItem>
        )}
        <MenuItem onClick={() => handleDialogOpen('delete')} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Futa
        </MenuItem>
      </Menu>

      {/* Confirmation Dialog */}
      <Dialog open={dialogOpen} onClose={handleDialogClose}>
        <DialogTitle>
          {dialogType === 'suspend' && 'Simamisha Mtumiaji'}
          {dialogType === 'activate' && 'Washa Mtumiaji'}
          {dialogType === 'delete' && 'Futa Mtumiaji'}
          {dialogType === 'edit' && 'Hariri Mtumiaji'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {dialogType === 'suspend' && `Una uhakika unataka kusimamisha ${selectedUser?.name}?`}
            {dialogType === 'activate' && `Una uhakika unataka kuwasha ${selectedUser?.name}?`}
            {dialogType === 'delete' && `Una uhakika unataka kumfuta ${selectedUser?.name}? Kitendo hiki hakiwezi kubatilishwa.`}
            {dialogType === 'edit' && 'Funguo za kuhariri mtumiaji zitaongezwa hapa.'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Ghairi</Button>
          <Button
            onClick={() => handleUserAction(dialogType)}
            color={dialogType === 'delete' ? 'error' : 'primary'}
            variant="contained"
          >
            {dialogType === 'suspend' && 'Simamisha'}
            {dialogType === 'activate' && 'Washa'}
            {dialogType === 'delete' && 'Futa'}
            {dialogType === 'edit' && 'Hifadhi'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
