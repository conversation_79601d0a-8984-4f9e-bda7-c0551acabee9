# ProChat - Project Structure Clarification

## 📁 CORRECT PROJECT STRUCTURE

```
ProChat/
├── backend/                    # Spring Boot API
│   ├── src/main/java/com/prochat/
│   │   ├── model/             # Database models
│   │   ├── controller/        # REST controllers
│   │   ├── service/           # Business logic
│   │   └── repository/        # Data access
│   └── pom.xml
│
├── mobile/                     # React Native Mobile App
│   ├── src/
│   │   ├── screens/           # Mobile screens
│   │   ├── components/        # Reusable components
│   │   └── navigation/        # Navigation setup
│   └── package.json
│
├── admin-panel/               # Admin Management System
│   ├── src/
│   │   ├── pages/            # Admin pages
│   │   ├── components/       # Admin components
│   │   └── contexts/         # Auth context
│   └── package.json
│
├── public-website/            # Public Website
│   ├── src/
│   │   ├── pages/            # Public pages
│   │   ├── components/       # Website components
│   │   └── styles/           # CSS/styling
│   └── package.json
│
└── database/
    └── init_database.sql      # Database schema
```

## 🎯 TOFAUTI KATI YA ADMIN PANEL NA PUBLIC WEBSITE

### 1. **ADMIN PANEL** (`admin-panel/`)

#### Lengo:
- Mfumo wa uongozi wa ndani wa ProChat platform
- Kudhibiti na kusimamia shughuli zote za platform

#### Watumiaji:
- **Super Admin** - Uongozi kamili
- **Moderator** - Udhibiti wa maudhui
- **Finance Officer** - Uongozi wa kifedha
- **Support Team** - Msaada kwa watumiaji
- **Event Officer** - Uongozi wa matukio
- **Journalist Admin** - Uongozi wa habari

#### Vipengele Vikuu:
- 📊 **Dashboard** - Takwimu za muda halisi
- 👥 **User Management** - Udhibiti wa watumiaji
- 🛡️ **Content Moderation** - Udhibiti wa maudhui
- 💰 **ProPay Finance** - Uongozi wa kifedha
- 🎫 **Event Management** - Uongozi wa matukio
- 💼 **Job Management** - Uongozi wa kazi
- 🎧 **Support Tickets** - Msaada kwa watumiaji
- ⚙️ **System Settings** - Mipangilio ya mfumo

#### Usalama:
- 🔐 2-Factor Authentication
- 🛡️ Role-based access control
- 📝 Complete audit logging
- 🌐 IP restrictions
- 🔒 Session management

### 2. **PUBLIC WEBSITE** (`public-website/`)

#### Lengo:
- Tovuti ya umma ya ProChat
- Kuonyesha vipengele vya platform kwa umma
- Kuwezesha download ya mobile app

#### Watumiaji:
- **Umma** - Watu wote
- **Watumiaji wa ProChat** - Waliojisajili
- **Wafanyabiashara** - Wanaotaka kutangaza
- **Waandaaji wa matukio** - Event organizers

#### Vipengele Vikuu:
- 🏠 **Home Page** - Ukurasa wa nyumbani
  - About ProChat
  - App download links (iOS/Android)
  - Features showcase
  - Testimonials

- 🔍 **Discover** - Gundua maudhui
  - Trending news
  - Popular channels
  - Featured stories
  - Public content

- 🎫 **Events** - Matukio ya umma
  - Browse events
  - Buy tickets
  - RSVP to events
  - Event calendar

- 💼 **Jobs & Tenders** - Kazi na zabuni
  - Job listings
  - Apply for jobs
  - Post job opportunities
  - Tender announcements

- 💌 **Invitations** - Mialiko ya kidijitali
  - Create digital invitations
  - Send via SMS/Email
  - RSVP management
  - Custom templates

#### Vipengele vya Mtumiaji:
- 📝 **Registration/Login** - Usajili/Kuingia
- ✅ **Verification Requests** - Ombi la uthibitisho
- 📰 **Submit News** - Wasilisha habari
- 🎪 **Submit Events** - Wasilisha matukio
- 💳 **ProPay Integration** - Malipo ya moja kwa moja

## 🔄 MUUNGANIKO KATI YA MIFUMO

### Data Flow:
```
Public Website → Backend API → Database
     ↓              ↓           ↓
Mobile App ←→ Backend API ←→ Database
     ↓              ↓           ↓
Admin Panel ←→ Backend API ←→ Database
```

### Integration Points:
1. **Shared Backend** - API moja kwa vyote
2. **Shared Database** - Database moja kwa data yote
3. **Shared Authentication** - JWT tokens
4. **Real-time Updates** - WebSocket connections

## 🚀 DEPLOYMENT STRATEGY

### Development:
- **Admin Panel**: `http://localhost:3001`
- **Public Website**: `http://localhost:3002`
- **Mobile App**: Expo/React Native
- **Backend API**: `http://localhost:8080`

### Production:
- **Admin Panel**: `https://admin.prochat.co.tz`
- **Public Website**: `https://prochat.co.tz`
- **Mobile App**: App Store/Play Store
- **Backend API**: `https://api.prochat.co.tz`

## 📱 USER JOURNEY

### Public User:
1. Visit `prochat.co.tz`
2. Learn about ProChat
3. Download mobile app
4. Register account
5. Use mobile app

### Business User:
1. Visit `prochat.co.tz`
2. Post job/event
3. Use ProPay for payments
4. Manage through website

### Admin User:
1. Access `admin.prochat.co.tz`
2. Login with 2FA
3. Manage platform
4. Monitor activities

## 🎯 SUMMARY

**Admin Panel** = Internal management system for ProChat staff
**Public Website** = External website for general public and users

Both connect to the same backend and database, but serve completely different purposes and audiences.

Nimesahihisha structure ili iwe wazi zaidi! 🎉
