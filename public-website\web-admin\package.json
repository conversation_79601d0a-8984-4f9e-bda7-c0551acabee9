{"name": "prochat-admin", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "axios": "^1.3.4", "chart.js": "^4.2.1", "react-chartjs-2": "^5.2.0", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "date-fns": "^2.29.3", "recharts": "^2.5.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}