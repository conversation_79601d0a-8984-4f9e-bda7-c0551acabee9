import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
  Chip,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  Security,
  Login as LoginIcon,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../contexts/AuthContext';

export default function Login() {
  const [showPassword, setShowPassword] = useState(false);
  const [show2FA, setShow2FA] = useState(false);
  const { login, loading } = useAuth();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm();

  const onSubmit = async (data) => {
    const result = await login(data);
    
    if (!result.success) {
      if (result.message?.includes('2FA')) {
        setShow2FA(true);
      } else {
        setError('root', { message: result.message });
      }
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <Card
          sx={{
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden',
          }}
        >
          {/* Header */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
              color: 'white',
              padding: 4,
              textAlign: 'center',
            }}
          >
            <AdminPanelSettings sx={{ fontSize: 60, mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom>
              ProChat Admin
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Mfumo wa Uongozi wa ProChat Platform
            </Typography>
          </Box>

          <CardContent sx={{ padding: 4 }}>
            {/* Admin Roles Info */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <Security sx={{ mr: 1 }} />
                Majukumu ya Uongozi
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Chip label="Super Admin" size="small" color="primary" />
                <Chip label="Moderator" size="small" color="secondary" />
                <Chip label="Finance Officer" size="small" color="success" />
                <Chip label="Support Team" size="small" color="info" />
                <Chip label="Event Officer" size="small" color="warning" />
                <Chip label="Journalist" size="small" color="error" />
              </Box>
              <Divider sx={{ my: 2 }} />
            </Box>

            {/* Login Form */}
            <form onSubmit={handleSubmit(onSubmit)}>
              {errors.root && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {errors.root.message}
                </Alert>
              )}

              <TextField
                fullWidth
                label="Jina la Mtumiaji au Barua Pepe"
                variant="outlined"
                margin="normal"
                {...register('username', {
                  required: 'Jina la mtumiaji ni lazima',
                })}
                error={!!errors.username}
                helperText={errors.username?.message}
                disabled={loading}
              />

              <TextField
                fullWidth
                label="Nywila"
                type={showPassword ? 'text' : 'password'}
                variant="outlined"
                margin="normal"
                {...register('password', {
                  required: 'Nywila ni lazima',
                  minLength: {
                    value: 6,
                    message: 'Nywila lazima iwe na angalau herufi 6',
                  },
                })}
                error={!!errors.password}
                helperText={errors.password?.message}
                disabled={loading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                        disabled={loading}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              {show2FA && (
                <TextField
                  fullWidth
                  label="Msimbo wa 2FA"
                  variant="outlined"
                  margin="normal"
                  {...register('twoFactorCode', {
                    required: 'Msimbo wa 2FA ni lazima',
                    pattern: {
                      value: /^\d{6}$/,
                      message: 'Msimbo lazima uwe wa nambari 6',
                    },
                  })}
                  error={!!errors.twoFactorCode}
                  helperText={errors.twoFactorCode?.message || 'Ingiza msimbo wa nambari 6 kutoka kwenye app yako ya 2FA'}
                  disabled={loading}
                  placeholder="123456"
                />
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
                sx={{
                  mt: 3,
                  mb: 2,
                  py: 1.5,
                  background: 'linear-gradient(135deg, #007AFF 0%, #4DA3FF 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #0056CC 0%, #007AFF 100%)',
                  },
                }}
              >
                {loading ? 'Inaingia...' : 'Ingia'}
              </Button>
            </form>

            {/* Security Notice */}
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Kumbuka:</strong> Mfumo huu una usalama wa hali ya juu. 
                Mazungumzo yako yanafuatiliwa kwa usalama. Usishirikishe taarifa za kuingia na mtu yeyote.
              </Typography>
            </Alert>

            {/* Footer */}
            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant="body2" color="text.secondary">
                ProChat Admin Panel v1.0.0
              </Typography>
              <Typography variant="body2" color="text.secondary">
                © 2024 ProChat. Haki zote zimehifadhiwa.
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
}
