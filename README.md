# ProChat - Social & Financial Platform

ProChat ni app ya kisasa inayochanganya huduma za mitandao ya kijamii na miamala ya kifedha. Lengo lake ni kuitikia mahitaji ya jamii ya Kitanzania na Afrika Mashariki kwa ujumla.

## Architecture

- **Frontend Mobile**: React Native
- **Frontend Web**: React (Admin Panel)
- **Backend**: Spring Boot
- **Database**: MySQL
- **Cloud Storage**: AWS S3

## Features

### Main Tabs
1. **Chats** - WhatsApp-style messaging
2. **Home** - Twitter-style social feed
3. **Discover** - Stories, news, videos, live streaming
4. **Me** - Profile, ProPay wallet, settings

### Key Features
- Digital Wallet (ProPay)
- Social Media Features
- Live Streaming
- Video Calls/Meetings
- Advertisement System
- Ticket Booking
- Agent Services (ProZone)

## Project Structure

```
ProChat/
├── backend/                 # Spring Boot API
├── mobile/                  # React Native App
├── web-admin/              # React Admin Panel
├── database/               # MySQL schemas and migrations
└── docs/                   # Documentation
```

## Getting Started

1. Clone the repository
2. Set up the backend (Spring Boot)
3. Set up the mobile app (React Native)
4. Set up the web admin (React)
5. Configure database (MySQL)
6. Configure AWS S3

## Development

Each component has its own README with specific setup instructions.
